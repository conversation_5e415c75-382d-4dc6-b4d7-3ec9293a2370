// 全局变量
let currentPage = 'home';
let currentPhotoIndex = 0;
let currentGalleryPage = 1;
let photosPerPage = 9;
let totalPages = 1;
let photos = [];
let favorites = [];

// 照片数据
const photoFiles = [
    '微信图片_20250704194141.jpg',
    '微信图片_20250704194143.jpg',
    '微信图片_20250704194145.jpg',
    '微信图片_20250704194149.jpg',
    '微信图片_20250704194151.jpg',
    '微信图片_20250704194155.jpg',
    '微信图片_20250704194159.jpg',
    '微信图片_20250704194203.jpg',
    '微信图片_20250704194208.jpg',
    '微信图片_20250704194213.jpg',
    '微信图片_20250704194214.jpg',
    '微信图片_20250704194215.jpg',
    '微信图片_20250704194216.jpg',
    '微信图片_20250704194217.jpg',
    '微信图片_20250704194218.jpg',
    '微信图片_20250704194219.jpg',
    '微信图片_20250704194220.jpg',
    '微信图片_20250704194221.jpg',
    '微信图片_20250704194224.jpg',
    '微信图片_20250704194228.jpg',
    '微信图片_20250704194230.jpg',
    '微信图片_20250704194234.jpg',
    '微信图片_20250704194237.jpg',
    '微信图片_20250704194238.jpg',
    '微信图片_20250704194242.jpg',
    '微信图片_20250704194245.jpg',
    '微信图片_20250704194246.jpg',
    '微信图片_20250704194248.jpg',
    '微信图片_20250704194249.jpg',
    '微信图片_20250704194354.jpg'
];

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    initializeApp();
    setupEventListeners();
    updateTime();
    setInterval(updateTime, 1000);
});

// 初始化应用
function initializeApp() {
    // 初始化照片数据
    photos = photoFiles.map((filename, index) => ({
        id: index,
        src: `image/${filename}`,
        title: `小宝的美好时光 ${index + 1}`,
        description: `记录小宝成长的珍贵瞬间 - 第${index + 1}张照片`,
        date: new Date(2024, 6, index + 1)
    }));

    // 加载收藏数据
    loadFavorites();
    
    // 计算总页数
    totalPages = Math.ceil(photos.length / photosPerPage);
    
    // 渲染相册
    renderPhotoGrid();
    renderPagination();
    
    // 更新统计数据
    updateStats();
    
    // 渲染收藏页面
    renderFavorites();
}

// 设置事件监听器
function setupEventListeners() {
    // 底部导航
    const navItems = document.querySelectorAll('.nav-item');
    navItems.forEach(item => {
        item.addEventListener('click', () => {
            const page = item.dataset.page;
            switchPage(page);
        });
    });

    // 相册控制
    const sortSelect = document.getElementById('sortSelect');
    const pageSizeSelect = document.getElementById('pageSizeSelect');
    
    if (sortSelect) {
        sortSelect.addEventListener('change', handleSortChange);
    }
    
    if (pageSizeSelect) {
        pageSizeSelect.addEventListener('change', handlePageSizeChange);
    }

    // 清空收藏按钮
    const clearFavoritesBtn = document.getElementById('clearFavoritesBtn');
    if (clearFavoritesBtn) {
        clearFavoritesBtn.addEventListener('click', clearAllFavorites);
    }
}

// 更新时间
function updateTime() {
    const now = new Date();
    const timeString = now.toLocaleTimeString('zh-CN', { 
        hour: '2-digit', 
        minute: '2-digit',
        hour12: false 
    });
    const timeElement = document.getElementById('currentTime');
    if (timeElement) {
        timeElement.textContent = timeString;
    }
}

// 页面切换
function switchPage(pageName) {
    if (pageName === currentPage) return;

    const currentPageEl = document.querySelector('.page.active');
    const targetPageEl = document.getElementById(pageName + 'Page');
    const navItems = document.querySelectorAll('.nav-item');

    if (!targetPageEl) return;

    // 更新导航状态
    navItems.forEach(item => {
        item.classList.toggle('active', item.dataset.page === pageName);
    });

    // 页面切换动画
    if (currentPageEl) {
        currentPageEl.classList.remove('active');
        currentPageEl.classList.add('prev');
        
        setTimeout(() => {
            currentPageEl.classList.remove('prev');
        }, 400);
    }

    targetPageEl.classList.add('active');
    currentPage = pageName;

    // 页面特定的初始化
    if (pageName === 'gallery') {
        renderPhotoGrid();
    } else if (pageName === 'favorites') {
        renderFavorites();
    }
}

// 渲染照片网格
function renderPhotoGrid() {
    const photoGrid = document.getElementById('photoGrid');
    if (!photoGrid) return;

    const startIndex = (currentGalleryPage - 1) * photosPerPage;
    const endIndex = startIndex + photosPerPage;
    const currentPhotos = photos.slice(startIndex, endIndex);

    photoGrid.innerHTML = '';

    currentPhotos.forEach((photo, index) => {
        const photoItem = document.createElement('div');
        photoItem.className = 'photo-item';
        photoItem.innerHTML = `
            <img src="${photo.src}" alt="${photo.title}" loading="lazy">
            <div class="photo-overlay">
                <div class="photo-overlay-content">
                    <h3>${photo.title}</h3>
                    <p>${photo.description}</p>
                </div>
            </div>
        `;

        const globalIndex = startIndex + index;
        photoItem.addEventListener('click', () => openPhotoViewer(globalIndex));
        photoGrid.appendChild(photoItem);
    });

    // 更新页面信息
    updatePageInfo();
}

// 渲染分页
function renderPagination() {
    const pagination = document.getElementById('pagination');
    if (!pagination) return;

    pagination.innerHTML = '';

    // 上一页按钮
    const prevBtn = document.createElement('button');
    prevBtn.className = 'page-btn';
    prevBtn.innerHTML = '‹';
    prevBtn.disabled = currentGalleryPage === 1;
    prevBtn.addEventListener('click', () => goToPage(currentGalleryPage - 1));
    pagination.appendChild(prevBtn);

    // 页码按钮
    const maxVisiblePages = 5;
    let startPage = Math.max(1, currentGalleryPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

    if (endPage - startPage + 1 < maxVisiblePages) {
        startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
        const pageBtn = document.createElement('button');
        pageBtn.className = `page-btn ${i === currentGalleryPage ? 'active' : ''}`;
        pageBtn.textContent = i;
        pageBtn.addEventListener('click', () => goToPage(i));
        pagination.appendChild(pageBtn);
    }

    // 下一页按钮
    const nextBtn = document.createElement('button');
    nextBtn.className = 'page-btn';
    nextBtn.innerHTML = '›';
    nextBtn.disabled = currentGalleryPage === totalPages;
    nextBtn.addEventListener('click', () => goToPage(currentGalleryPage + 1));
    pagination.appendChild(nextBtn);
}

// 跳转到指定页面
function goToPage(page) {
    if (page < 1 || page > totalPages || page === currentGalleryPage) return;

    currentGalleryPage = page;
    renderPhotoGrid();
    renderPagination();
}

// 更新页面信息
function updatePageInfo() {
    const currentPageNumEl = document.getElementById('currentPageNum');
    const totalPhotosCountEl = document.getElementById('totalPhotosCount');

    if (currentPageNumEl) currentPageNumEl.textContent = currentGalleryPage;
    if (totalPhotosCountEl) totalPhotosCountEl.textContent = photos.length;
}

// 处理排序变化
function handleSortChange(e) {
    const sortType = e.target.value;
    
    switch(sortType) {
        case 'date':
            photos.sort((a, b) => b.date - a.date);
            break;
        case 'name':
            photos.sort((a, b) => a.title.localeCompare(b.title));
            break;
        case 'random':
            photos = shuffleArray(photos);
            break;
        default:
            // 恢复默认顺序
            photos.sort((a, b) => a.id - b.id);
            break;
    }
    
    currentGalleryPage = 1;
    renderPhotoGrid();
    renderPagination();
}

// 处理每页数量变化
function handlePageSizeChange(e) {
    photosPerPage = parseInt(e.target.value);
    totalPages = Math.ceil(photos.length / photosPerPage);
    currentGalleryPage = 1;
    renderPhotoGrid();
    renderPagination();
}

// 洗牌算法
function shuffleArray(array) {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
}

// 打开照片查看器
function openPhotoViewer(index) {
    currentPhotoIndex = index;
    const photoViewer = document.getElementById('photoViewer');
    const viewerPhoto = document.getElementById('viewerPhoto');
    const photoTitle = document.getElementById('photoTitle');
    const photoDescription = document.getElementById('photoDescription');
    const viewerPhotoIndex = document.getElementById('viewerPhotoIndex');
    const viewerPhotoTotal = document.getElementById('viewerPhotoTotal');
    const viewerFavoriteBtn = document.getElementById('viewerFavoriteBtn');

    if (!photoViewer || !photos[index]) return;

    const photo = photos[index];

    // 设置照片信息
    viewerPhoto.src = photo.src;
    viewerPhoto.alt = photo.title;
    photoTitle.textContent = photo.title;
    photoDescription.textContent = photo.description;
    viewerPhotoIndex.textContent = index + 1;
    viewerPhotoTotal.textContent = photos.length;

    // 更新收藏按钮状态
    updateFavoriteButton();

    // 显示查看器
    photoViewer.classList.add('active');
    document.body.style.overflow = 'hidden';
}

// 关闭照片查看器
function closePhotoViewer() {
    const photoViewer = document.getElementById('photoViewer');
    photoViewer.classList.remove('active');
    document.body.style.overflow = '';
}

// 导航照片
function navigatePhoto(direction) {
    const newIndex = currentPhotoIndex + direction;
    
    if (newIndex >= 0 && newIndex < photos.length) {
        openPhotoViewer(newIndex);
    }
}

// 切换照片收藏状态
function togglePhotoFavorite() {
    const photoId = photos[currentPhotoIndex].id;
    const index = favorites.indexOf(photoId);
    
    if (index > -1) {
        favorites.splice(index, 1);
        showToast('已从收藏中移除');
    } else {
        favorites.push(photoId);
        showToast('已添加到收藏');
    }
    
    saveFavorites();
    updateFavoriteButton();
    updateStats();
    renderFavorites();
}

// 更新收藏按钮状态
function updateFavoriteButton() {
    const viewerFavoriteBtn = document.getElementById('viewerFavoriteBtn');
    if (!viewerFavoriteBtn || !photos[currentPhotoIndex]) return;
    
    const photoId = photos[currentPhotoIndex].id;
    const isFavorite = favorites.includes(photoId);
    
    viewerFavoriteBtn.style.color = isFavorite ? '#ff6b9d' : 'rgba(255, 255, 255, 0.7)';
    viewerFavoriteBtn.title = isFavorite ? '取消收藏' : '添加收藏';
}

// 加载收藏数据
function loadFavorites() {
    const saved = localStorage.getItem('babyPhotoFavorites');
    favorites = saved ? JSON.parse(saved) : [];
}

// 保存收藏数据
function saveFavorites() {
    localStorage.setItem('babyPhotoFavorites', JSON.stringify(favorites));
}

// 更新统计数据
function updateStats() {
    const favoriteCountEl = document.getElementById('favoriteCount');
    const navFavoriteBadge = document.getElementById('navFavoriteBadge');
    const totalPhotosEl = document.getElementById('totalPhotos');

    if (favoriteCountEl) favoriteCountEl.textContent = favorites.length;
    if (totalPhotosEl) totalPhotosEl.textContent = photos.length;
    
    if (navFavoriteBadge) {
        navFavoriteBadge.textContent = favorites.length;
        navFavoriteBadge.classList.toggle('show', favorites.length > 0);
    }
}

// 渲染收藏页面
function renderFavorites() {
    const favoritesContent = document.getElementById('favoritesContent');
    const emptyFavorites = document.getElementById('emptyFavorites');
    const favoritesGrid = document.getElementById('favoritesGrid');

    if (!favoritesContent) return;

    if (favorites.length === 0) {
        emptyFavorites.style.display = 'block';
        favoritesGrid.style.display = 'none';
    } else {
        emptyFavorites.style.display = 'none';
        favoritesGrid.style.display = 'grid';
        
        favoritesGrid.innerHTML = '';
        
        favorites.forEach(photoId => {
            const photo = photos.find(p => p.id === photoId);
            if (!photo) return;
            
            const favoriteItem = document.createElement('div');
            favoriteItem.className = 'favorite-item';
            favoriteItem.innerHTML = `
                <img src="${photo.src}" alt="${photo.title}">
                <div class="favorite-overlay">❤️</div>
            `;
            
            favoriteItem.addEventListener('click', () => openPhotoViewer(photo.id));
            favoritesGrid.appendChild(favoriteItem);
        });
    }
}

// 清空所有收藏
function clearAllFavorites() {
    if (favorites.length === 0) return;
    
    if (confirm('确定要清空所有收藏吗？')) {
        favorites = [];
        saveFavorites();
        updateStats();
        renderFavorites();
        showToast('已清空所有收藏');
    }
}

// 显示提示消息
function showToast(message) {
    const toast = document.getElementById('toast');
    const toastContent = document.getElementById('toastContent');
    
    if (!toast || !toastContent) return;
    
    toastContent.textContent = message;
    toast.classList.add('show');
    
    setTimeout(() => {
        toast.classList.remove('show');
    }, 2000);
}
