<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小宝的成长记录 - 珍贵时光</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
</head>
<body>
    <!-- 加载动画 -->
    <div class="loader-wrapper">
        <div class="loader">
            <div class="loader-heart">
                <div class="heart-beat"></div>
            </div>
            <p class="loader-text">小宝的世界正在加载...</p>
        </div>
    </div>

    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <span class="logo-icon">👶</span>
                <span class="logo-text">小宝</span>
            </div>
            <div class="nav-menu">
                <a href="#home" class="nav-link">首页</a>
                <a href="#gallery" class="nav-link">相册</a>
                <a href="#about" class="nav-link">关于</a>
                <a href="#timeline" class="nav-link">成长</a>
            </div>
            <div class="nav-toggle">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <!-- 主页面 -->
    <main class="main-content">
        <!-- 英雄区域 -->
        <section id="home" class="hero-section">
            <div class="hero-background">
                <div class="floating-shapes">
                    <div class="shape shape-1"></div>
                    <div class="shape shape-2"></div>
                    <div class="shape shape-3"></div>
                    <div class="shape shape-4"></div>
                </div>
            </div>
            <div class="hero-content">
                <div class="hero-text">
                    <h1 class="hero-title">
                        <span class="title-line">欢迎来到</span>
                        <span class="title-line highlight">小宝的世界</span>
                    </h1>
                    <p class="hero-subtitle">记录每一个珍贵的成长瞬间</p>
                    <div class="hero-buttons">
                        <button class="btn btn-primary" onclick="scrollToSection('gallery')">
                            <span>探索相册</span>
                            <i class="btn-icon">📸</i>
                        </button>
                        <button class="btn btn-secondary" onclick="scrollToSection('about')">
                            <span>了解小宝</span>
                            <i class="btn-icon">💝</i>
                        </button>
                    </div>
                </div>
                <div class="hero-image">
                    <div class="image-container">
                        <img src="image/微信图片_20250704194141.jpg" alt="小宝" class="hero-img">
                        <div class="image-overlay"></div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 相册区域 -->
        <section id="gallery" class="gallery-section">
            <div class="section-header">
                <h2 class="section-title">
                    <span class="title-icon">📸</span>
                    小宝的精彩瞬间
                </h2>
                <p class="section-subtitle">每一张照片都承载着珍贵的回忆</p>

                <!-- 相册控制栏 -->
                <div class="gallery-controls">
                    <div class="view-mode-toggle">
                        <button class="mode-btn active" data-mode="grid" title="网格视图">
                            <i class="icon">⊞</i>
                        </button>
                        <button class="mode-btn" data-mode="masonry" title="瀑布流">
                            <i class="icon">⊟</i>
                        </button>
                        <button class="mode-btn" data-mode="slideshow" title="幻灯片">
                            <i class="icon">▶</i>
                        </button>
                    </div>

                    <div class="gallery-info">
                        <span class="photo-count">共 <span id="totalPhotos">0</span> 张照片</span>
                        <span class="current-page">第 <span id="currentPageNum">1</span> 页</span>
                    </div>

                    <div class="gallery-filters">
                        <select class="filter-select" id="sortFilter">
                            <option value="default">默认排序</option>
                            <option value="name">按名称</option>
                            <option value="date">按日期</option>
                            <option value="random">随机排序</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="gallery-container">
                <!-- 照片网格 -->
                <div class="gallery-grid" id="galleryGrid">
                    <!-- 照片将通过JavaScript动态加载 -->
                </div>

                <!-- 分页控件 -->
                <div class="pagination-container">
                    <div class="pagination" id="pagination">
                        <!-- 分页按钮将通过JavaScript生成 -->
                    </div>
                    <div class="pagination-info">
                        <span>每页显示</span>
                        <select class="page-size-select" id="pageSizeSelect">
                            <option value="6">6张</option>
                            <option value="9" selected>9张</option>
                            <option value="12">12张</option>
                            <option value="18">18张</option>
                        </select>
                        <span>张照片</span>
                    </div>
                </div>
            </div>

            <!-- 全屏照片查看器 -->
            <div class="photo-viewer" id="photoViewer">
                <div class="viewer-overlay"></div>
                <div class="viewer-content">
                    <div class="viewer-header">
                        <div class="viewer-title-bar">
                            <h3 class="viewer-title"></h3>
                            <div class="viewer-counter">
                                <span id="currentPhotoIndex">1</span> / <span id="totalPhotoCount">1</span>
                            </div>
                        </div>
                        <div class="viewer-controls">
                            <button class="viewer-btn favorite-btn" title="收藏" onclick="toggleFavorite(currentPhotoIndex)">❤️</button>
                            <button class="viewer-btn share-btn" title="分享">📤</button>
                            <button class="viewer-btn download-btn" title="下载">💾</button>
                            <button class="viewer-btn fullscreen-btn" title="全屏">⛶</button>
                            <button class="viewer-btn viewer-close" title="关闭">&times;</button>
                        </div>
                    </div>

                    <div class="viewer-main">
                        <button class="viewer-nav viewer-prev" title="上一张">
                            <span class="nav-icon">‹</span>
                        </button>

                        <div class="viewer-image-container">
                            <img src="" alt="" class="viewer-image">
                            <div class="image-loading">
                                <div class="loading-spinner"></div>
                                <p>加载中...</p>
                            </div>
                        </div>

                        <button class="viewer-nav viewer-next" title="下一张">
                            <span class="nav-icon">›</span>
                        </button>
                    </div>

                    <div class="viewer-footer">
                        <div class="viewer-description"></div>
                        <div class="viewer-thumbnails" id="viewerThumbnails">
                            <!-- 缩略图将通过JavaScript生成 -->
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 关于小宝 -->
        <section id="about" class="about-section">
            <div class="about-container">
                <div class="about-content">
                    <div class="about-text">
                        <h2 class="about-title">关于小宝</h2>
                        <div class="about-description">
                            <p>小宝是一个充满活力和好奇心的小天使，每天都在用纯真的眼神探索这个美丽的世界。</p>
                            <p>这里记录着小宝成长路上的每一个珍贵瞬间，从第一次微笑到第一次学步，每一个里程碑都值得被永远珍藏。</p>
                        </div>
                        <div class="about-stats">
                            <div class="stat-item">
                                <div class="stat-number" data-target="365">0</div>
                                <div class="stat-label">快乐的日子</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number" data-target="1000">0</div>
                                <div class="stat-label">珍贵照片</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number" data-target="100">0</div>
                                <div class="stat-label">美好回忆</div>
                            </div>
                        </div>
                    </div>
                    <div class="about-image">
                        <div class="image-collage">
                            <img src="image/微信图片_20250704194143.jpg" alt="小宝" class="collage-img img-1">
                            <img src="image/微信图片_20250704194145.jpg" alt="小宝" class="collage-img img-2">
                            <img src="image/微信图片_20250704194149.jpg" alt="小宝" class="collage-img img-3">
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 成长时间线 -->
        <section id="timeline" class="timeline-section">
            <div class="section-header">
                <h2 class="section-title">成长足迹</h2>
                <p class="section-subtitle">记录小宝成长的每一个重要时刻</p>
            </div>
            
            <div class="timeline-container">
                <div class="timeline">
                    <div class="timeline-item">
                        <div class="timeline-marker"></div>
                        <div class="timeline-content">
                            <div class="timeline-date">2024年6月</div>
                            <h3 class="timeline-title">第一次微笑</h3>
                            <p class="timeline-description">小宝第一次对着镜头露出了甜美的笑容</p>
                        </div>
                    </div>
                    
                    <div class="timeline-item">
                        <div class="timeline-marker"></div>
                        <div class="timeline-content">
                            <div class="timeline-date">2024年7月</div>
                            <h3 class="timeline-title">学会坐立</h3>
                            <p class="timeline-description">小宝可以独自坐着玩耍了</p>
                        </div>
                    </div>
                    
                    <div class="timeline-item">
                        <div class="timeline-marker"></div>
                        <div class="timeline-content">
                            <div class="timeline-date">2024年8月</div>
                            <h3 class="timeline-title">第一次爬行</h3>
                            <p class="timeline-description">小宝开始探索更大的世界</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- 底部菜单栏 -->
    <div class="bottom-menu" id="bottomMenu">
        <div class="menu-item" data-section="home">
            <div class="menu-icon">🏠</div>
            <span class="menu-label">首页</span>
        </div>
        <div class="menu-item active" data-section="gallery">
            <div class="menu-icon">📸</div>
            <span class="menu-label">相册</span>
        </div>
        <div class="menu-item" data-section="about">
            <div class="menu-icon">👶</div>
            <span class="menu-label">关于</span>
        </div>
        <div class="menu-item" data-section="timeline">
            <div class="menu-icon">📅</div>
            <span class="menu-label">成长</span>
        </div>
        <div class="menu-item" id="favoritesMenu">
            <div class="menu-icon">❤️</div>
            <span class="menu-label">收藏</span>
            <div class="favorite-count" id="favoriteCount">0</div>
        </div>
    </div>

    <!-- 页脚 -->
    <footer class="footer">
        <div class="footer-content">
            <p>&copy; 2025 小宝的成长记录. 用爱记录每一个瞬间 💕</p>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/animejs/3.2.1/anime.min.js"></script>
    <script src="script.js"></script>
</body>
</html>
