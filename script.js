// 全局变量
let currentPhotoIndex = 0;
let photos = [];
let isLoading = true;
let currentPage = 1;
let photosPerPage = 9;
let totalPages = 1;
let currentViewMode = 'grid';
let currentSort = 'default';

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

// 初始化应用
function initializeApp() {
    // 显示加载动画
    showLoader();
    
    // 初始化照片数据
    initializePhotos();
    
    // 设置导航
    setupNavigation();
    
    // 设置滚动动画
    setupScrollAnimations();
    
    // 设置照片查看器
    setupPhotoViewer();
    
    // 设置统计数字动画
    setupStatsAnimation();
    
    // 页面加载完成后隐藏加载动画
    setTimeout(() => {
        hideLoader();
        startPageAnimations();
    }, 2000);
}

// 显示加载动画
function showLoader() {
    const loader = document.querySelector('.loader-wrapper');
    if (loader) {
        loader.style.display = 'flex';
        
        // 心跳动画
        anime({
            targets: '.heart-beat',
            scale: [1, 1.3, 1],
            duration: 1500,
            easing: 'easeInOutQuad',
            loop: true
        });
        
        // 文字淡入动画
        anime({
            targets: '.loader-text',
            opacity: [0, 1],
            translateY: [20, 0],
            duration: 1000,
            delay: 500,
            easing: 'easeOutQuad'
        });
    }
}

// 隐藏加载动画
function hideLoader() {
    const loader = document.querySelector('.loader-wrapper');
    if (loader) {
        anime({
            targets: loader,
            opacity: 0,
            duration: 500,
            easing: 'easeOutQuad',
            complete: () => {
                loader.style.display = 'none';
                isLoading = false;
            }
        });
    }
}

// 开始页面动画
function startPageAnimations() {
    // 导航栏动画
    anime({
        targets: '.navbar',
        translateY: [-70, 0],
        opacity: [0, 1],
        duration: 800,
        easing: 'easeOutQuad'
    });
    
    // 英雄区域标题动画
    anime({
        targets: '.title-line',
        opacity: [0, 1],
        translateY: [50, 0],
        duration: 1000,
        delay: anime.stagger(200),
        easing: 'easeOutQuad'
    });
    
    // 英雄区域副标题和按钮动画
    anime({
        targets: ['.hero-subtitle', '.hero-buttons'],
        opacity: [0, 1],
        translateY: [30, 0],
        duration: 800,
        delay: 800,
        easing: 'easeOutQuad'
    });
    
    // 英雄图片动画
    anime({
        targets: '.hero-image',
        opacity: [0, 1],
        scale: [0.8, 1],
        duration: 1000,
        delay: 600,
        easing: 'easeOutQuad'
    });
    
    // 浮动形状动画
    animateFloatingShapes();
}

// 浮动形状动画
function animateFloatingShapes() {
    anime({
        targets: '.shape',
        translateY: [
            { value: -20, duration: 3000 },
            { value: 0, duration: 3000 }
        ],
        rotate: [
            { value: 180, duration: 6000 }
        ],
        opacity: [0.1, 0.3, 0.1],
        duration: 6000,
        loop: true,
        easing: 'easeInOutSine',
        delay: anime.stagger(1000)
    });
}

// 初始化照片数据
function initializePhotos() {
    // 获取所有照片文件名
    const photoFiles = [
        '微信图片_20250704194141.jpg',
        '微信图片_20250704194143.jpg',
        '微信图片_20250704194145.jpg',
        '微信图片_20250704194149.jpg',
        '微信图片_20250704194151.jpg',
        '微信图片_20250704194155.jpg',
        '微信图片_20250704194159.jpg',
        '微信图片_20250704194203.jpg',
        '微信图片_20250704194208.jpg',
        '微信图片_20250704194213.jpg',
        '微信图片_20250704194214.jpg',
        '微信图片_20250704194215.jpg',
        '微信图片_20250704194216.jpg',
        '微信图片_20250704194217.jpg',
        '微信图片_20250704194218.jpg',
        '微信图片_20250704194219.jpg',
        '微信图片_20250704194220.jpg',
        '微信图片_20250704194221.jpg',
        '微信图片_20250704194224.jpg',
        '微信图片_20250704194228.jpg',
        '微信图片_20250704194230.jpg',
        '微信图片_20250704194234.jpg',
        '微信图片_20250704194237.jpg',
        '微信图片_20250704194238.jpg',
        '微信图片_20250704194242.jpg',
        '微信图片_20250704194245.jpg',
        '微信图片_20250704194246.jpg',
        '微信图片_20250704194248.jpg',
        '微信图片_20250704194249.jpg',
        '微信图片_20250704194354.jpg'
    ];

    // 创建照片对象数组
    photos = photoFiles.map((filename, index) => ({
        id: index + 1,
        src: `image/${filename}`,
        title: `小宝的美好时光 ${index + 1}`,
        description: `记录小宝成长的珍贵瞬间 - 第${index + 1}张照片`,
        date: new Date(2024, 6, index + 1), // 模拟日期
        favorite: false
    }));

    // 计算总页数
    totalPages = Math.ceil(photos.length / photosPerPage);

    // 更新UI
    updatePhotoCount();
    setupGalleryControls();
    renderPhotoGrid();
    renderPagination();
}

// 渲染照片网格
function renderPhotoGrid() {
    const galleryGrid = document.getElementById('galleryGrid');
    if (!galleryGrid) return;

    // 获取当前页的照片
    const startIndex = (currentPage - 1) * photosPerPage;
    const endIndex = startIndex + photosPerPage;
    const currentPhotos = getSortedPhotos().slice(startIndex, endIndex);

    galleryGrid.innerHTML = '';

    // 添加加载状态
    currentPhotos.forEach((photo) => {
        const galleryItem = document.createElement('div');
        galleryItem.className = 'gallery-item loading';
        galleryItem.innerHTML = `
            <img src="${photo.src}" alt="${photo.title}" class="gallery-image" loading="lazy" onload="this.parentElement.classList.remove('loading')">
            <div class="gallery-overlay">
                <div class="gallery-overlay-content">
                    <h3>${photo.title}</h3>
                    <p>${photo.description}</p>
                    <div class="photo-meta">
                        <span class="photo-date">${formatDate(photo.date)}</span>
                        ${photo.favorite ? '<span class="favorite-indicator">❤️</span>' : ''}
                    </div>
                </div>
            </div>
        `;

        const globalIndex = photos.findIndex(p => p.id === photo.id);
        galleryItem.addEventListener('click', () => openPhotoViewer(globalIndex));
        galleryGrid.appendChild(galleryItem);
    });

    // 照片网格动画
    setTimeout(() => {
        anime({
            targets: '.gallery-item',
            opacity: [0, 1],
            translateY: [50, 0],
            scale: [0.9, 1],
            duration: 600,
            delay: anime.stagger(80),
            easing: 'easeOutQuad'
        });
    }, 100);
}

// 获取排序后的照片
function getSortedPhotos() {
    let sortedPhotos = [...photos];

    switch(currentSort) {
        case 'name':
            sortedPhotos.sort((a, b) => a.title.localeCompare(b.title));
            break;
        case 'date':
            sortedPhotos.sort((a, b) => b.date - a.date);
            break;
        case 'random':
            sortedPhotos = shuffleArray(sortedPhotos);
            break;
        default:
            // 保持默认顺序
            break;
    }

    return sortedPhotos;
}

// 洗牌算法
function shuffleArray(array) {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
}

// 格式化日期
function formatDate(date) {
    return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}

// 更新照片计数
function updatePhotoCount() {
    const totalPhotosEl = document.getElementById('totalPhotos');
    const currentPageNumEl = document.getElementById('currentPageNum');

    if (totalPhotosEl) totalPhotosEl.textContent = photos.length;
    if (currentPageNumEl) currentPageNumEl.textContent = currentPage;
}

// 设置相册控件
function setupGalleryControls() {
    // 视图模式切换
    const modeButtons = document.querySelectorAll('.mode-btn');
    modeButtons.forEach(btn => {
        btn.addEventListener('click', () => {
            modeButtons.forEach(b => b.classList.remove('active'));
            btn.classList.add('active');
            currentViewMode = btn.dataset.mode;
            applyViewMode();
        });
    });

    // 排序选择
    const sortFilter = document.getElementById('sortFilter');
    if (sortFilter) {
        sortFilter.addEventListener('change', (e) => {
            currentSort = e.target.value;
            currentPage = 1; // 重置到第一页
            renderPhotoGrid();
            renderPagination();
            updatePhotoCount();
        });
    }

    // 每页显示数量
    const pageSizeSelect = document.getElementById('pageSizeSelect');
    if (pageSizeSelect) {
        pageSizeSelect.addEventListener('change', (e) => {
            photosPerPage = parseInt(e.target.value);
            totalPages = Math.ceil(photos.length / photosPerPage);
            currentPage = 1; // 重置到第一页
            renderPhotoGrid();
            renderPagination();
            updatePhotoCount();
        });
    }
}

// 应用视图模式
function applyViewMode() {
    const galleryGrid = document.getElementById('galleryGrid');
    if (!galleryGrid) return;

    galleryGrid.className = 'gallery-grid';

    switch(currentViewMode) {
        case 'masonry':
            galleryGrid.classList.add('masonry-view');
            break;
        case 'slideshow':
            startAutoSlideshow();
            break;
        default:
            galleryGrid.classList.add('grid-view');
            break;
    }
}

// 渲染分页
function renderPagination() {
    const pagination = document.getElementById('pagination');
    if (!pagination) return;

    pagination.innerHTML = '';

    // 上一页按钮
    const prevBtn = document.createElement('button');
    prevBtn.className = 'page-btn';
    prevBtn.innerHTML = '‹';
    prevBtn.disabled = currentPage === 1;
    prevBtn.addEventListener('click', () => goToPage(currentPage - 1));
    pagination.appendChild(prevBtn);

    // 页码按钮
    const maxVisiblePages = 5;
    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

    if (endPage - startPage + 1 < maxVisiblePages) {
        startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    // 第一页
    if (startPage > 1) {
        const firstBtn = document.createElement('button');
        firstBtn.className = 'page-btn';
        firstBtn.textContent = '1';
        firstBtn.addEventListener('click', () => goToPage(1));
        pagination.appendChild(firstBtn);

        if (startPage > 2) {
            const ellipsis = document.createElement('span');
            ellipsis.className = 'page-ellipsis';
            ellipsis.textContent = '...';
            pagination.appendChild(ellipsis);
        }
    }

    // 中间页码
    for (let i = startPage; i <= endPage; i++) {
        const pageBtn = document.createElement('button');
        pageBtn.className = `page-btn ${i === currentPage ? 'active' : ''}`;
        pageBtn.textContent = i;
        pageBtn.addEventListener('click', () => goToPage(i));
        pagination.appendChild(pageBtn);
    }

    // 最后一页
    if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
            const ellipsis = document.createElement('span');
            ellipsis.className = 'page-ellipsis';
            ellipsis.textContent = '...';
            pagination.appendChild(ellipsis);
        }

        const lastBtn = document.createElement('button');
        lastBtn.className = 'page-btn';
        lastBtn.textContent = totalPages;
        lastBtn.addEventListener('click', () => goToPage(totalPages));
        pagination.appendChild(lastBtn);
    }

    // 下一页按钮
    const nextBtn = document.createElement('button');
    nextBtn.className = 'page-btn';
    nextBtn.innerHTML = '›';
    nextBtn.disabled = currentPage === totalPages;
    nextBtn.addEventListener('click', () => goToPage(currentPage + 1));
    pagination.appendChild(nextBtn);
}

// 跳转到指定页面
function goToPage(page) {
    if (page < 1 || page > totalPages || page === currentPage) return;

    currentPage = page;
    updatePhotoCount();
    renderPhotoGrid();
    renderPagination();

    // 滚动到相册顶部
    const gallerySection = document.getElementById('gallery');
    if (gallerySection) {
        gallerySection.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
}

// 设置导航
function setupNavigation() {
    const navLinks = document.querySelectorAll('.nav-link');
    const navToggle = document.querySelector('.nav-toggle');
    const navMenu = document.querySelector('.nav-menu');
    
    // 平滑滚动
    navLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            const targetId = link.getAttribute('href').substring(1);
            scrollToSection(targetId);
        });
    });
    
    // 移动端菜单切换
    if (navToggle) {
        navToggle.addEventListener('click', () => {
            navMenu.classList.toggle('active');
            
            // 汉堡菜单动画
            anime({
                targets: navToggle.children,
                rotate: navMenu.classList.contains('active') ? 45 : 0,
                duration: 300,
                easing: 'easeOutQuad'
            });
        });
    }
}

// 滚动到指定区域
function scrollToSection(sectionId) {
    const section = document.getElementById(sectionId);
    if (section) {
        const offsetTop = section.offsetTop - 70; // 考虑导航栏高度
        
        anime({
            targets: document.documentElement,
            scrollTop: offsetTop,
            duration: 1000,
            easing: 'easeInOutQuad'
        });
    }
}

// 设置滚动动画
function setupScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const target = entry.target;
                
                // 区域标题动画
                if (target.classList.contains('section-header')) {
                    anime({
                        targets: target.children,
                        opacity: [0, 1],
                        translateY: [30, 0],
                        duration: 800,
                        delay: anime.stagger(200),
                        easing: 'easeOutQuad'
                    });
                }
                
                // 关于区域动画
                if (target.classList.contains('about-content')) {
                    anime({
                        targets: target.children,
                        opacity: [0, 1],
                        translateX: [-50, 0],
                        duration: 1000,
                        delay: anime.stagger(300),
                        easing: 'easeOutQuad'
                    });
                }
                
                // 时间线动画
                if (target.classList.contains('timeline-item')) {
                    anime({
                        targets: target,
                        opacity: [0, 1],
                        translateX: [-30, 0],
                        duration: 800,
                        easing: 'easeOutQuad'
                    });
                }
                
                observer.unobserve(target);
            }
        });
    }, observerOptions);
    
    // 观察需要动画的元素
    document.querySelectorAll('.section-header, .about-content, .timeline-item').forEach(el => {
        observer.observe(el);
    });
}

// 设置统计数字动画
function setupStatsAnimation() {
    const statNumbers = document.querySelectorAll('.stat-number');
    
    const statsObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const target = entry.target;
                const finalValue = parseInt(target.getAttribute('data-target'));
                
                anime({
                    targets: { value: 0 },
                    value: finalValue,
                    duration: 2000,
                    easing: 'easeOutQuad',
                    update: function(anim) {
                        target.textContent = Math.round(anim.animatables[0].target.value);
                    }
                });
                
                statsObserver.unobserve(target);
            }
        });
    });
    
    statNumbers.forEach(stat => {
        statsObserver.observe(stat);
    });
}

// 设置照片查看器
function setupPhotoViewer() {
    const photoViewer = document.getElementById('photoViewer');
    const viewerClose = document.querySelector('.viewer-close');
    const viewerPrev = document.querySelector('.viewer-prev');
    const viewerNext = document.querySelector('.viewer-next');
    const viewerOverlay = document.querySelector('.viewer-overlay');

    // 关闭查看器
    [viewerClose, viewerOverlay].forEach(element => {
        if (element) {
            element.addEventListener('click', closePhotoViewer);
        }
    });

    // 上一张/下一张
    if (viewerPrev) {
        viewerPrev.addEventListener('click', () => navigatePhoto(-1));
    }

    if (viewerNext) {
        viewerNext.addEventListener('click', () => navigatePhoto(1));
    }

    // 键盘导航
    document.addEventListener('keydown', (e) => {
        if (!photoViewer.classList.contains('active')) return;

        switch(e.key) {
            case 'Escape':
                closePhotoViewer();
                break;
            case 'ArrowLeft':
                navigatePhoto(-1);
                break;
            case 'ArrowRight':
                navigatePhoto(1);
                break;
        }
    });

    // 触摸手势支持
    setupTouchGestures();
}

// 打开照片查看器
function openPhotoViewer(index) {
    currentPhotoIndex = index;
    const photoViewer = document.getElementById('photoViewer');
    const viewerImage = document.querySelector('.viewer-image');
    const viewerTitle = document.querySelector('.viewer-title');
    const viewerDescription = document.querySelector('.viewer-description');
    const currentPhotoIndexEl = document.getElementById('currentPhotoIndex');
    const totalPhotoCountEl = document.getElementById('totalPhotoCount');
    const imageLoading = document.querySelector('.image-loading');

    if (!photoViewer || !photos[index]) return;

    // 显示加载状态
    if (imageLoading) imageLoading.style.display = 'block';

    // 设置照片信息
    const photo = photos[index];
    viewerTitle.textContent = photo.title;
    viewerDescription.textContent = photo.description;

    // 更新计数器
    if (currentPhotoIndexEl) currentPhotoIndexEl.textContent = index + 1;
    if (totalPhotoCountEl) totalPhotoCountEl.textContent = photos.length;

    // 加载图片
    const img = new Image();
    img.onload = () => {
        viewerImage.src = photo.src;
        viewerImage.alt = photo.title;
        if (imageLoading) imageLoading.style.display = 'none';

        // 图片加载完成动画
        anime({
            targets: viewerImage,
            scale: [0.9, 1],
            opacity: [0, 1],
            duration: 400,
            easing: 'easeOutQuad'
        });
    };
    img.src = photo.src;

    // 显示查看器
    photoViewer.style.display = 'flex';
    document.body.style.overflow = 'hidden';

    // 动画效果
    anime({
        targets: photoViewer,
        opacity: [0, 1],
        duration: 300,
        easing: 'easeOutQuad',
        complete: () => {
            photoViewer.classList.add('active');
        }
    });

    // 生成缩略图
    generateThumbnails();

    // 更新收藏状态
    updateFavoriteButton();
}

// 生成缩略图
function generateThumbnails() {
    const thumbnailsContainer = document.getElementById('viewerThumbnails');
    if (!thumbnailsContainer) return;

    thumbnailsContainer.innerHTML = '';

    photos.forEach((photo, index) => {
        const thumbnailItem = document.createElement('div');
        thumbnailItem.className = `thumbnail-item ${index === currentPhotoIndex ? 'active' : ''}`;
        thumbnailItem.innerHTML = `<img src="${photo.src}" alt="${photo.title}">`;

        thumbnailItem.addEventListener('click', () => {
            navigateToPhoto(index);
        });

        thumbnailsContainer.appendChild(thumbnailItem);
    });

    // 滚动到当前缩略图
    const activeThumbnail = thumbnailsContainer.querySelector('.active');
    if (activeThumbnail) {
        activeThumbnail.scrollIntoView({ behavior: 'smooth', inline: 'center' });
    }
}

// 导航到指定照片
function navigateToPhoto(index) {
    if (index < 0 || index >= photos.length || index === currentPhotoIndex) return;

    const viewerImage = document.querySelector('.viewer-image');
    const viewerTitle = document.querySelector('.viewer-title');
    const viewerDescription = document.querySelector('.viewer-description');
    const currentPhotoIndexEl = document.getElementById('currentPhotoIndex');
    const imageLoading = document.querySelector('.image-loading');

    // 显示加载状态
    if (imageLoading) imageLoading.style.display = 'block';

    // 淡出当前照片
    anime({
        targets: viewerImage,
        opacity: 0,
        scale: 0.95,
        duration: 200,
        easing: 'easeOutQuad',
        complete: () => {
            // 更新照片信息
            currentPhotoIndex = index;
            const photo = photos[index];

            // 加载新图片
            const img = new Image();
            img.onload = () => {
                viewerImage.src = photo.src;
                viewerImage.alt = photo.title;
                viewerTitle.textContent = photo.title;
                viewerDescription.textContent = photo.description;

                if (currentPhotoIndexEl) currentPhotoIndexEl.textContent = index + 1;
                if (imageLoading) imageLoading.style.display = 'none';

                // 淡入新照片
                anime({
                    targets: viewerImage,
                    opacity: [0, 1],
                    scale: [0.95, 1],
                    duration: 300,
                    easing: 'easeOutQuad'
                });

                // 更新缩略图状态
                updateThumbnailsState();
                updateFavoriteButton();
            };
            img.src = photo.src;
        }
    });
}

// 更新缩略图状态
function updateThumbnailsState() {
    const thumbnails = document.querySelectorAll('.thumbnail-item');
    thumbnails.forEach((thumb, index) => {
        thumb.classList.toggle('active', index === currentPhotoIndex);
    });

    // 滚动到当前缩略图
    const activeThumbnail = document.querySelector('.thumbnail-item.active');
    if (activeThumbnail) {
        activeThumbnail.scrollIntoView({ behavior: 'smooth', inline: 'center' });
    }
}

// 更新收藏按钮状态
function updateFavoriteButton() {
    const favoriteBtn = document.querySelector('.favorite-btn');
    if (favoriteBtn && photos[currentPhotoIndex]) {
        const isFavorite = photos[currentPhotoIndex].favorite;
        favoriteBtn.style.color = isFavorite ? '#ff6b9d' : 'rgba(255, 255, 255, 0.7)';
        favoriteBtn.title = isFavorite ? '取消收藏' : '添加收藏';
    }
}

// 关闭照片查看器
function closePhotoViewer() {
    const photoViewer = document.getElementById('photoViewer');
    if (!photoViewer) return;

    anime({
        targets: photoViewer,
        opacity: [1, 0],
        duration: 300,
        easing: 'easeOutQuad',
        complete: () => {
            photoViewer.style.display = 'none';
            photoViewer.classList.remove('active');
            document.body.style.overflow = '';
        }
    });
}

// 导航照片
function navigatePhoto(direction) {
    const newIndex = currentPhotoIndex + direction;

    if (newIndex >= 0 && newIndex < photos.length) {
        navigateToPhoto(newIndex);
    }
}

// 设置触摸手势
function setupTouchGestures() {
    const photoViewer = document.getElementById('photoViewer');
    if (!photoViewer) return;

    let startX = 0;
    let startY = 0;
    let isSwipe = false;

    photoViewer.addEventListener('touchstart', (e) => {
        startX = e.touches[0].clientX;
        startY = e.touches[0].clientY;
        isSwipe = false;
    });

    photoViewer.addEventListener('touchmove', (e) => {
        if (!startX || !startY) return;

        const currentX = e.touches[0].clientX;
        const currentY = e.touches[0].clientY;

        const diffX = startX - currentX;
        const diffY = startY - currentY;

        if (Math.abs(diffX) > Math.abs(diffY) && Math.abs(diffX) > 50) {
            isSwipe = true;
            e.preventDefault();
        }
    });

    photoViewer.addEventListener('touchend', (e) => {
        if (!isSwipe || !startX) return;

        const endX = e.changedTouches[0].clientX;
        const diffX = startX - endX;

        if (Math.abs(diffX) > 50) {
            if (diffX > 0) {
                navigatePhoto(1); // 向左滑动，下一张
            } else {
                navigatePhoto(-1); // 向右滑动，上一张
            }
        }

        startX = 0;
        startY = 0;
        isSwipe = false;
    });
}

// 按钮点击效果
document.addEventListener('click', (e) => {
    if (e.target.classList.contains('btn')) {
        // 按钮点击波纹效果
        const button = e.target;
        const rect = button.getBoundingClientRect();
        const ripple = document.createElement('span');

        ripple.style.position = 'absolute';
        ripple.style.borderRadius = '50%';
        ripple.style.background = 'rgba(255, 255, 255, 0.6)';
        ripple.style.transform = 'scale(0)';
        ripple.style.animation = 'ripple 0.6s linear';
        ripple.style.left = (e.clientX - rect.left) + 'px';
        ripple.style.top = (e.clientY - rect.top) + 'px';

        button.appendChild(ripple);

        setTimeout(() => {
            ripple.remove();
        }, 600);
    }
});

// 添加波纹动画CSS
const style = document.createElement('style');
style.textContent = `
    @keyframes ripple {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
`;
document.head.appendChild(style);

// 滚动时导航栏效果
let lastScrollTop = 0;
window.addEventListener('scroll', () => {
    const navbar = document.querySelector('.navbar');
    const scrollTop = window.scrollY || document.documentElement.scrollTop;

    if (scrollTop > lastScrollTop && scrollTop > 100) {
        // 向下滚动，隐藏导航栏
        navbar.style.transform = 'translateY(-100%)';
    } else {
        // 向上滚动，显示导航栏
        navbar.style.transform = 'translateY(0)';
    }

    // 滚动时改变导航栏背景透明度
    if (scrollTop > 50) {
        navbar.style.background = 'rgba(255, 255, 255, 0.98)';
        navbar.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.1)';
    } else {
        navbar.style.background = 'rgba(255, 255, 255, 0.95)';
        navbar.style.boxShadow = 'none';
    }

    lastScrollTop = scrollTop;
});

// 懒加载图片
function setupLazyLoading() {
    const images = document.querySelectorAll('img[loading="lazy"]');

    const imageObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;

                // 图片加载动画
                anime({
                    targets: img,
                    opacity: [0, 1],
                    scale: [0.9, 1],
                    duration: 600,
                    easing: 'easeOutQuad'
                });

                imageObserver.unobserve(img);
            }
        });
    });

    images.forEach(img => {
        img.style.opacity = '0';
        imageObserver.observe(img);
    });
}

// 页面完全加载后设置懒加载
window.addEventListener('load', () => {
    setupLazyLoading();
    setupParallaxEffect();
    setupPhotoSlideshow();
});

// 视差滚动效果
function setupParallaxEffect() {
    const parallaxElements = document.querySelectorAll('.hero-background, .floating-shapes');

    window.addEventListener('scroll', () => {
        const scrolled = window.scrollY;
        const rate = scrolled * -0.5;

        parallaxElements.forEach(element => {
            element.style.transform = `translateY(${rate}px)`;
        });
    });
}

// 照片幻灯片自动播放
function setupPhotoSlideshow() {
    let slideshowInterval;
    let isAutoPlaying = false;

    // 创建控制按钮
    const controlsContainer = document.createElement('div');
    controlsContainer.className = 'slideshow-controls';
    controlsContainer.innerHTML = `
        <button class="slideshow-btn play-btn" title="自动播放">
            <span class="play-icon">▶️</span>
            <span class="pause-icon" style="display: none;">⏸️</span>
        </button>
        <button class="slideshow-btn shuffle-btn" title="随机播放">🔀</button>
        <div class="slideshow-progress">
            <div class="progress-bar"></div>
        </div>
    `;

    const gallerySection = document.querySelector('.gallery-section');
    if (gallerySection) {
        gallerySection.appendChild(controlsContainer);
    }

    // 播放/暂停功能
    const playBtn = document.querySelector('.play-btn');
    const playIcon = document.querySelector('.play-icon');
    const pauseIcon = document.querySelector('.pause-icon');
    const progressBar = document.querySelector('.progress-bar');

    if (playBtn) {
        playBtn.addEventListener('click', () => {
            if (isAutoPlaying) {
                stopSlideshow();
            } else {
                startSlideshow();
            }
        });
    }

    // 随机播放
    const shuffleBtn = document.querySelector('.shuffle-btn');
    if (shuffleBtn) {
        shuffleBtn.addEventListener('click', () => {
            shufflePhotos();
        });
    }

    function startSlideshow() {
        isAutoPlaying = true;
        playIcon.style.display = 'none';
        pauseIcon.style.display = 'inline';

        let progress = 0;
        const duration = 5000; // 5秒切换一次
        const interval = 50;

        slideshowInterval = setInterval(() => {
            progress += interval;
            const percentage = (progress / duration) * 100;
            progressBar.style.width = percentage + '%';

            if (progress >= duration) {
                progress = 0;
                // 自动切换到下一张照片
                const randomIndex = Math.floor(Math.random() * photos.length);
                openPhotoViewer(randomIndex);
            }
        }, interval);
    }

    function stopSlideshow() {
        isAutoPlaying = false;
        playIcon.style.display = 'inline';
        pauseIcon.style.display = 'none';
        clearInterval(slideshowInterval);
        progressBar.style.width = '0%';
    }

    function shufflePhotos() {
        // Fisher-Yates 洗牌算法
        for (let i = photos.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [photos[i], photos[j]] = [photos[j], photos[i]];
        }
        renderPhotoGrid();

        // 显示洗牌动画
        anime({
            targets: '.gallery-item',
            scale: [1, 0.8, 1],
            rotate: [0, 360, 0],
            duration: 800,
            delay: anime.stagger(50),
            easing: 'easeInOutQuad'
        });
    }
}

// 添加照片收藏功能
function setupPhotoFavorites() {
    let favorites = JSON.parse(localStorage.getItem('photoFavorites') || '[]');

    // 在照片查看器中添加收藏按钮
    const viewerContent = document.querySelector('.viewer-content');
    if (viewerContent) {
        const favoriteBtn = document.createElement('button');
        favoriteBtn.className = 'favorite-btn';
        favoriteBtn.innerHTML = '❤️';
        favoriteBtn.title = '收藏照片';
        viewerContent.appendChild(favoriteBtn);

        favoriteBtn.addEventListener('click', () => {
            toggleFavorite(currentPhotoIndex);
        });
    }

    function toggleFavorite(photoIndex) {
        const favoriteBtn = document.querySelector('.favorite-btn');

        if (favorites.includes(photoIndex)) {
            favorites = favorites.filter(index => index !== photoIndex);
            favoriteBtn.style.color = '#ccc';
            showNotification('已取消收藏');
        } else {
            favorites.push(photoIndex);
            favoriteBtn.style.color = '#ff6b9d';
            showNotification('已添加到收藏');

            // 收藏动画
            anime({
                targets: favoriteBtn,
                scale: [1, 1.3, 1],
                duration: 300,
                easing: 'easeOutQuad'
            });
        }

        localStorage.setItem('photoFavorites', JSON.stringify(favorites));
        updateFavoriteStatus();
    }

    function updateFavoriteStatus() {
        const favoriteBtn = document.querySelector('.favorite-btn');
        if (favoriteBtn) {
            favoriteBtn.style.color = favorites.includes(currentPhotoIndex) ? '#ff6b9d' : '#ccc';
        }
    }

    // 在照片查看器打开时更新收藏状态
    const originalOpenPhotoViewer = openPhotoViewer;
    openPhotoViewer = function(index) {
        originalOpenPhotoViewer(index);
        setTimeout(updateFavoriteStatus, 100);
    };
}

// 通知系统
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;

    document.body.appendChild(notification);

    // 显示动画
    anime({
        targets: notification,
        translateY: [-50, 0],
        opacity: [0, 1],
        duration: 300,
        easing: 'easeOutQuad'
    });

    // 自动隐藏
    setTimeout(() => {
        anime({
            targets: notification,
            translateY: [0, -50],
            opacity: [0],
            duration: 300,
            easing: 'easeOutQuad',
            complete: () => {
                notification.remove();
            }
        });
    }, 3000);
}

// 添加照片分享功能
function setupPhotoSharing() {
    const shareBtn = document.createElement('button');
    shareBtn.className = 'share-btn';
    shareBtn.innerHTML = '📤';
    shareBtn.title = '分享照片';

    const viewerContent = document.querySelector('.viewer-content');
    if (viewerContent) {
        viewerContent.appendChild(shareBtn);

        shareBtn.addEventListener('click', () => {
            sharePhoto(currentPhotoIndex);
        });
    }
}

function sharePhoto(photoIndex) {
    const photo = photos[photoIndex];

    if (navigator.share) {
        // 使用原生分享API
        navigator.share({
            title: photo.title,
            text: photo.description,
            url: window.location.href
        }).catch(err => console.log('分享失败:', err));
    } else {
        // 复制链接到剪贴板
        navigator.clipboard.writeText(window.location.href).then(() => {
            showNotification('链接已复制到剪贴板');
        }).catch(() => {
            showNotification('分享失败', 'error');
        });
    }
}

// 添加全屏功能
function setupFullscreen() {
    const fullscreenBtn = document.createElement('button');
    fullscreenBtn.className = 'fullscreen-btn';
    fullscreenBtn.innerHTML = '⛶';
    fullscreenBtn.title = '全屏查看';

    const viewerContent = document.querySelector('.viewer-content');
    if (viewerContent) {
        viewerContent.appendChild(fullscreenBtn);

        fullscreenBtn.addEventListener('click', () => {
            toggleFullscreen();
        });
    }
}

function toggleFullscreen() {
    const photoViewer = document.getElementById('photoViewer');

    if (!document.fullscreenElement) {
        photoViewer.requestFullscreen().catch(err => {
            console.log('全屏失败:', err);
        });
    } else {
        document.exitFullscreen();
    }
}

// 初始化所有高级功能
document.addEventListener('DOMContentLoaded', () => {
    setupPhotoFavorites();
    setupPhotoSharing();
    setupFullscreen();
});
