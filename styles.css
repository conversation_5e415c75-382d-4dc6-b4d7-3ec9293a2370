/* 全局样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #ff6b9d;
    --secondary-color: #4ecdc4;
    --accent-color: #ffd93d;
    --text-dark: #2c3e50;
    --text-light: #7f8c8d;
    --white: #ffffff;
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --gradient-accent: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --shadow-light: 0 4px 20px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 8px 30px rgba(0, 0, 0, 0.15);
    --shadow-heavy: 0 15px 50px rgba(0, 0, 0, 0.2);
    --border-radius: 20px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    color: var(--text-dark);
    overflow-x: hidden;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* 加载动画 */
.loader-wrapper {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    opacity: 1;
    transition: opacity 0.5s ease;
}

.loader {
    text-align: center;
    color: var(--white);
}

.loader-heart {
    width: 80px;
    height: 80px;
    margin: 0 auto 20px;
    position: relative;
}

.heart-beat {
    width: 100%;
    height: 100%;
    background: var(--white);
    border-radius: 50%;
    animation: heartbeat 1.5s ease-in-out infinite;
}

.heart-beat::before {
    content: '💖';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 2rem;
}

@keyframes heartbeat {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.2); }
}

.loader-text {
    font-size: 1.2rem;
    font-weight: 300;
    opacity: 0.9;
}

/* 导航栏 */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    z-index: 1000;
    transition: var(--transition);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 600;
    font-size: 1.5rem;
    color: var(--primary-color);
}

.logo-icon {
    font-size: 2rem;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

.nav-menu {
    display: flex;
    gap: 30px;
}

.nav-link {
    text-decoration: none;
    color: var(--text-dark);
    font-weight: 500;
    position: relative;
    transition: var(--transition);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient-secondary);
    transition: width 0.3s ease;
}

.nav-link:hover::after {
    width: 100%;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.nav-toggle span {
    width: 25px;
    height: 3px;
    background: var(--text-dark);
    margin: 3px 0;
    transition: var(--transition);
}

/* 主内容 */
.main-content {
    padding-top: 70px;
}

/* 英雄区域 */
.hero-section {
    min-height: 100vh;
    position: relative;
    display: flex;
    align-items: center;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    z-index: -1;
}

.floating-shapes {
    position: absolute;
    width: 100%;
    height: 100%;
}

.shape {
    position: absolute;
    border-radius: 50%;
    opacity: 0.1;
    animation: float 6s ease-in-out infinite;
}

.shape-1 {
    width: 100px;
    height: 100px;
    background: var(--white);
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.shape-2 {
    width: 150px;
    height: 150px;
    background: var(--accent-color);
    top: 60%;
    right: 10%;
    animation-delay: 2s;
}

.shape-3 {
    width: 80px;
    height: 80px;
    background: var(--secondary-color);
    bottom: 20%;
    left: 20%;
    animation-delay: 4s;
}

.shape-4 {
    width: 120px;
    height: 120px;
    background: var(--white);
    top: 10%;
    right: 30%;
    animation-delay: 1s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

.hero-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.hero-text {
    color: var(--white);
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 20px;
}

.title-line {
    display: block;
    opacity: 0;
    transform: translateY(50px);
}

.highlight {
    background: var(--gradient-accent);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.3rem;
    font-weight: 300;
    margin-bottom: 40px;
    opacity: 0.9;
}

.hero-buttons {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.btn {
    padding: 15px 30px;
    border: none;
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 10px;
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--gradient-secondary);
    color: var(--white);
    box-shadow: var(--shadow-medium);
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-heavy);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.2);
    color: var(--white);
    border: 2px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-3px);
}

.hero-image {
    position: relative;
}

.image-container {
    position: relative;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-heavy);
    transform: perspective(1000px) rotateY(-15deg);
    transition: var(--transition);
}

.image-container:hover {
    transform: perspective(1000px) rotateY(0deg) scale(1.05);
}

.hero-img {
    width: 100%;
    height: 500px;
    object-fit: cover;
    display: block;
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-accent);
    opacity: 0.2;
    transition: var(--transition);
}

.image-container:hover .image-overlay {
    opacity: 0;
}

/* 通用区域样式 */
.section-header {
    text-align: center;
    margin-bottom: 60px;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 15px;
    background: var(--gradient-secondary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section-subtitle {
    font-size: 1.2rem;
    color: var(--text-light);
    font-weight: 300;
}

/* 相册区域 */
.gallery-section {
    padding: 100px 20px;
    background: var(--white);
}

.gallery-container {
    max-width: 1200px;
    margin: 0 auto;
}

.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-bottom: 50px;
}

.gallery-item {
    position: relative;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-light);
    transition: var(--transition);
    cursor: pointer;
    background: var(--white);
}

.gallery-item:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-heavy);
}

.gallery-image {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: var(--transition);
}

.gallery-item:hover .gallery-image {
    transform: scale(1.1);
}

.gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    opacity: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
}

.gallery-item:hover .gallery-overlay {
    opacity: 0.9;
}

.gallery-overlay-content {
    text-align: center;
    color: var(--white);
    transform: translateY(20px);
    transition: var(--transition);
}

.gallery-item:hover .gallery-overlay-content {
    transform: translateY(0);
}

.gallery-overlay h3 {
    font-size: 1.3rem;
    margin-bottom: 10px;
}

.gallery-overlay p {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* 照片查看器 */
.photo-viewer {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2000;
    display: none;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.photo-viewer.active {
    display: flex;
    opacity: 1;
}

.viewer-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(10px);
}

.viewer-content {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.viewer-close {
    position: absolute;
    top: 20px;
    right: 20px;
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 50%;
    color: var(--white);
    font-size: 1.5rem;
    cursor: pointer;
    transition: var(--transition);
    z-index: 10;
}

.viewer-close:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.viewer-prev,
.viewer-next {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 50%;
    color: var(--white);
    font-size: 1.5rem;
    cursor: pointer;
    transition: var(--transition);
    z-index: 10;
}

.viewer-prev {
    left: 20px;
}

.viewer-next {
    right: 20px;
}

.viewer-prev:hover,
.viewer-next:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-50%) scale(1.1);
}

.viewer-image-container {
    max-width: 90%;
    max-height: 80%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.viewer-image {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-heavy);
}

.viewer-info {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    color: var(--white);
    background: rgba(0, 0, 0, 0.5);
    padding: 20px;
    border-radius: var(--border-radius);
    backdrop-filter: blur(10px);
}

.viewer-title {
    font-size: 1.2rem;
    margin-bottom: 5px;
}

.viewer-description {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }

    .nav-toggle {
        display: flex;
    }

    .hero-content {
        grid-template-columns: 1fr;
        gap: 40px;
        text-align: center;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-buttons {
        justify-content: center;
    }

    .image-container {
        transform: none;
    }

    .hero-img {
        height: 300px;
    }

    .gallery-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .section-title {
        font-size: 2rem;
    }

    .viewer-prev,
    .viewer-next {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    .viewer-info {
        bottom: 10px;
        left: 10px;
        right: 10px;
        transform: none;
        padding: 15px;
    }
}

/* 关于区域 */
.about-section {
    padding: 100px 20px;
    background: var(--gradient-accent);
    color: var(--white);
}

.about-container {
    max-width: 1200px;
    margin: 0 auto;
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.about-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 30px;
}

.about-description {
    font-size: 1.1rem;
    line-height: 1.8;
    margin-bottom: 40px;
    opacity: 0.9;
}

.about-description p {
    margin-bottom: 20px;
}

.about-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
}

.stat-item {
    text-align: center;
    padding: 20px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius);
    backdrop-filter: blur(10px);
    transition: var(--transition);
}

.stat-item:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.2);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
    color: var(--accent-color);
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
}

.about-image {
    position: relative;
}

.image-collage {
    position: relative;
    height: 400px;
}

.collage-img {
    position: absolute;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-heavy);
    transition: var(--transition);
    object-fit: cover;
}

.img-1 {
    width: 200px;
    height: 250px;
    top: 0;
    left: 0;
    z-index: 3;
}

.img-2 {
    width: 180px;
    height: 220px;
    top: 50px;
    right: 50px;
    z-index: 2;
}

.img-3 {
    width: 160px;
    height: 200px;
    bottom: 0;
    left: 100px;
    z-index: 1;
}

.collage-img:hover {
    transform: scale(1.05) rotate(5deg);
    z-index: 10;
}

/* 时间线区域 */
.timeline-section {
    padding: 100px 20px;
    background: var(--white);
}

.timeline-container {
    max-width: 800px;
    margin: 0 auto;
}

.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: var(--gradient-secondary);
}

.timeline-item {
    position: relative;
    margin-bottom: 50px;
    padding-left: 40px;
}

.timeline-marker {
    position: absolute;
    left: -40px;
    top: 0;
    width: 20px;
    height: 20px;
    background: var(--primary-color);
    border-radius: 50%;
    border: 4px solid var(--white);
    box-shadow: var(--shadow-light);
    z-index: 2;
}

.timeline-content {
    background: var(--white);
    padding: 30px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    transition: var(--transition);
    position: relative;
}

.timeline-content::before {
    content: '';
    position: absolute;
    left: -10px;
    top: 20px;
    width: 0;
    height: 0;
    border-top: 10px solid transparent;
    border-bottom: 10px solid transparent;
    border-right: 10px solid var(--white);
}

.timeline-item:hover .timeline-content {
    transform: translateX(10px);
    box-shadow: var(--shadow-medium);
}

.timeline-date {
    color: var(--primary-color);
    font-weight: 600;
    font-size: 0.9rem;
    margin-bottom: 10px;
}

.timeline-title {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 10px;
    color: var(--text-dark);
}

.timeline-description {
    color: var(--text-light);
    line-height: 1.6;
}

/* 页脚 */
.footer {
    background: var(--text-dark);
    color: var(--white);
    padding: 40px 20px;
    text-align: center;
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
}

.footer p {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* 移动端响应式补充 */
@media (max-width: 768px) {
    .about-content {
        grid-template-columns: 1fr;
        gap: 40px;
        text-align: center;
    }

    .about-stats {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .image-collage {
        height: 300px;
    }

    .img-1 {
        width: 150px;
        height: 180px;
    }

    .img-2 {
        width: 130px;
        height: 160px;
        top: 30px;
        right: 30px;
    }

    .img-3 {
        width: 120px;
        height: 140px;
        bottom: 0;
        left: 60px;
    }

    .timeline {
        padding-left: 20px;
    }

    .timeline::before {
        left: 10px;
    }

    .timeline-item {
        padding-left: 30px;
    }

    .timeline-marker {
        left: -30px;
        width: 15px;
        height: 15px;
    }

    .timeline-content {
        padding: 20px;
    }
}

/* 移动端专用优化 */
@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .btn {
        padding: 12px 20px;
        font-size: 0.9rem;
    }

    .section-title {
        font-size: 1.8rem;
    }

    .section-subtitle {
        font-size: 1rem;
    }

    .about-title {
        font-size: 2rem;
    }

    .about-description {
        font-size: 1rem;
    }

    .stat-number {
        font-size: 2rem;
    }

    .timeline-title {
        font-size: 1.1rem;
    }

    .timeline-description {
        font-size: 0.9rem;
    }
}

/* 触摸优化 */
.btn, .nav-link, .gallery-item, .timeline-item {
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
}

/* 滚动优化 */
html {
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
}

/* 移动端导航菜单 */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        top: 70px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 70px);
        background: rgba(255, 255, 255, 0.98);
        backdrop-filter: blur(20px);
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding-top: 50px;
        transition: left 0.3s ease;
        z-index: 999;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-link {
        font-size: 1.2rem;
        padding: 15px 0;
        width: 100%;
        text-align: center;
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    }

    .nav-toggle.active span:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }

    .nav-toggle.active span:nth-child(2) {
        opacity: 0;
    }

    .nav-toggle.active span:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
    }
}

/* 性能优化 */
.gallery-image,
.hero-img,
.collage-img,
.viewer-image {
    will-change: transform;
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
}

/* 动画性能优化 */
.shape,
.floating-shapes,
.gallery-item,
.timeline-item {
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
}

/* 高分辨率屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .hero-img,
    .gallery-image,
    .collage-img {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* 暗色模式支持 */
@media (prefers-color-scheme: dark) {
    :root {
        --text-dark: #ffffff;
        --text-light: #b0b0b0;
        --white: #1a1a1a;
    }

    body {
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        color: var(--text-dark);
    }

    .navbar {
        background: rgba(26, 26, 26, 0.95);
    }

    .gallery-section,
    .timeline-section {
        background: #1a1a1a;
    }

    .gallery-item,
    .timeline-content {
        background: #2c2c2c;
        color: var(--text-dark);
    }

    .footer {
        background: #0f0f0f;
    }
}

/* 减少动画效果（用户偏好） */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .shape {
        animation: none;
    }

    .heart-beat {
        animation: none;
    }

    .logo-icon {
        animation: none;
    }
}

/* 打印样式 */
@media print {
    .navbar,
    .loader-wrapper,
    .photo-viewer,
    .floating-shapes {
        display: none !important;
    }

    .hero-section,
    .gallery-section,
    .about-section,
    .timeline-section {
        page-break-inside: avoid;
    }

    body {
        background: white !important;
        color: black !important;
    }
}

/* 幻灯片控制样式 */
.slideshow-controls {
    position: fixed;
    bottom: 20px;
    right: 20px;
    display: flex;
    flex-direction: column;
    gap: 10px;
    z-index: 1500;
}

.slideshow-btn {
    width: 50px;
    height: 50px;
    border: none;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow-medium);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

.slideshow-btn:hover {
    transform: scale(1.1);
    background: rgba(255, 255, 255, 1);
}

.slideshow-progress {
    width: 50px;
    height: 4px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: var(--gradient-secondary);
    width: 0%;
    transition: width 0.1s linear;
}

/* 查看器按钮样式 */
.favorite-btn,
.share-btn,
.fullscreen-btn {
    position: absolute;
    top: 80px;
    right: 20px;
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 50%;
    color: var(--white);
    font-size: 1.5rem;
    cursor: pointer;
    transition: var(--transition);
    z-index: 10;
}

.share-btn {
    top: 140px;
}

.fullscreen-btn {
    top: 200px;
}

.favorite-btn:hover,
.share-btn:hover,
.fullscreen-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

/* 通知样式 */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    background: var(--gradient-secondary);
    color: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-heavy);
    z-index: 3000;
    font-weight: 500;
    max-width: 300px;
}

.notification-error {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
}

.notification-success {
    background: linear-gradient(135deg, #51cf66 0%, #40c057 100%);
}

/* 移动端控制按钮优化 */
@media (max-width: 768px) {
    .slideshow-controls {
        bottom: 10px;
        right: 10px;
    }

    .slideshow-btn {
        width: 45px;
        height: 45px;
        font-size: 1rem;
    }

    .favorite-btn,
    .share-btn,
    .fullscreen-btn {
        width: 45px;
        height: 45px;
        font-size: 1.2rem;
        right: 10px;
    }

    .share-btn {
        top: 120px;
    }

    .fullscreen-btn {
        top: 175px;
    }

    .notification {
        top: 10px;
        right: 10px;
        left: 10px;
        max-width: none;
        font-size: 0.9rem;
    }
}

/* 加载状态样式 */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: loading-shimmer 1.5s infinite;
}

@keyframes loading-shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* 高级交互效果 */
.gallery-item {
    position: relative;
    overflow: hidden;
}

.gallery-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at center, transparent 0%, rgba(255, 107, 157, 0.1) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.gallery-item:hover::before {
    opacity: 1;
}

/* 3D 翻转效果 */
.flip-card {
    perspective: 1000px;
}

.flip-card-inner {
    position: relative;
    width: 100%;
    height: 100%;
    text-align: center;
    transition: transform 0.6s;
    transform-style: preserve-3d;
}

.flip-card:hover .flip-card-inner {
    transform: rotateY(180deg);
}

.flip-card-front,
.flip-card-back {
    position: absolute;
    width: 100%;
    height: 100%;
    backface-visibility: hidden;
    border-radius: var(--border-radius);
}

.flip-card-back {
    transform: rotateY(180deg);
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
}

/* 磁性吸附效果 */
.magnetic {
    transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.magnetic:hover {
    transform: scale(1.05);
}

/* 粒子效果容器 */
.particles-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    overflow: hidden;
}

.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: var(--accent-color);
    border-radius: 50%;
    opacity: 0.7;
    animation: float-particle 6s infinite linear;
}

@keyframes float-particle {
    0% {
        transform: translateY(100vh) rotate(0deg);
        opacity: 0;
    }
    10% {
        opacity: 0.7;
    }
    90% {
        opacity: 0.7;
    }
    100% {
        transform: translateY(-100px) rotate(360deg);
        opacity: 0;
    }
}
