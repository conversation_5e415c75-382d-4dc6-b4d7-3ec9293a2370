/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* CSS变量 - 高端配色方案 */
:root {
    /* 主色调 - 深邃优雅 */
    --primary-color: #0a0a0a;
    --secondary-color: #1a1a1a;
    --accent-color: #2a2a2a;

    /* 背景色 - 纯净简约 */
    --background-color: #ffffff;
    --surface-color: #fafafa;
    --surface-elevated: #ffffff;

    /* 文字颜色 - 层次分明 */
    --text-primary: #0a0a0a;
    --text-secondary: #6a6a6a;
    --text-tertiary: #9a9a9a;
    --text-inverse: #ffffff;

    /* 强调色 - 精致点缀 */
    --highlight-color: #007aff;
    --success-color: #34c759;
    --warning-color: #ff9500;
    --error-color: #ff3b30;

    /* 边框和分割线 */
    --border-color: #e5e5e7;
    --border-light: #f2f2f7;
    --divider-color: #d1d1d6;

    /* 阴影系统 - 精细层次 */
    --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.04);
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.08);
    --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.12);
    --shadow-xl: 0 16px 32px rgba(0, 0, 0, 0.16);

    /* 渐变色 - 现代感 */
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-warm: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    --gradient-cool: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    --gradient-dark: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);

    /* 圆角系统 */
    --radius-xs: 4px;
    --radius-sm: 8px;
    --radius-md: 12px;
    --radius-lg: 16px;
    --radius-xl: 24px;
    --radius-full: 9999px;

    /* 动画曲线 */
    --ease-out: cubic-bezier(0.25, 0.46, 0.45, 0.94);
    --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
    --ease-spring: cubic-bezier(0.68, -0.55, 0.265, 1.55);

    /* 间距系统 */
    --space-xs: 4px;
    --space-sm: 8px;
    --space-md: 16px;
    --space-lg: 24px;
    --space-xl: 32px;
    --space-2xl: 48px;
}

/* 基础样式 */
body {
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Helvetica Neue', sans-serif;
    background: var(--background-color);
    color: var(--text-primary);
    line-height: 1.5;
    overflow: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-feature-settings: 'kern' 1, 'liga' 1;
}

/* 应用容器 */
.app-container {
    width: 100vw;
    height: 100vh;
    display: flex;
    flex-direction: column;
    background: var(--background-color);
    position: relative;
    overflow: hidden;
}

/* 页面容器 */
.page-container {
    flex: 1;
    position: relative;
    overflow: hidden;
}

/* 页面基础样式 */
.page {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--background-color);
    transform: translateX(100%);
    transition: transform 0.5s var(--ease-out);
    overflow-y: auto;
    padding-bottom: 100px;
    -webkit-overflow-scrolling: touch;
}

.page.active {
    transform: translateX(0);
}

.page.prev {
    transform: translateX(-100%);
}

/* 页面头部 */
.page-header {
    padding: 20px 20px 10px;
    background: var(--surface-color);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: sticky;
    top: 0;
    z-index: 100;
    backdrop-filter: blur(10px);
}

.page-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 8px;
}

.page-subtitle {
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin-top: 4px;
}

.title-icon {
    font-size: 1.3rem;
}

.header-actions {
    display: flex;
    gap: 8px;
}

.header-btn {
    width: 36px;
    height: 36px;
    border: none;
    border-radius: var(--border-radius-small);
    background: var(--background-color);
    color: var(--text-primary);
    font-size: 1.1rem;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.header-btn:hover {
    background: var(--border-color);
    transform: scale(1.05);
}

/* 首页样式 - 全新设计 */
.home-page {
    padding: 0;
    background: var(--background-color);
}

/* 主视觉区域 */
.hero-section {
    position: relative;
    padding: var(--space-2xl) var(--space-lg);
    min-height: 50vh;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 0;
}

.gradient-orb {
    position: absolute;
    border-radius: 50%;
    filter: blur(60px);
    opacity: 0.6;
    animation: float 6s ease-in-out infinite;
}

.orb-1 {
    width: 200px;
    height: 200px;
    background: var(--gradient-primary);
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.orb-2 {
    width: 150px;
    height: 150px;
    background: var(--gradient-warm);
    top: 60%;
    right: 20%;
    animation-delay: 2s;
}

.orb-3 {
    width: 100px;
    height: 100px;
    background: var(--gradient-cool);
    bottom: 20%;
    left: 60%;
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-20px) rotate(120deg); }
    66% { transform: translateY(10px) rotate(240deg); }
}

.hero-content {
    position: relative;
    z-index: 1;
    text-align: center;
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.8);
    border-radius: var(--radius-xl);
    padding: var(--space-xl);
    border: 1px solid var(--border-light);
    box-shadow: var(--shadow-lg);
}

/* 头像区域 */
.hero-avatar {
    position: relative;
    margin-bottom: var(--space-lg);
}

.avatar-ring {
    width: 120px;
    height: 120px;
    margin: 0 auto;
    position: relative;
    border-radius: 50%;
    background: var(--gradient-primary);
    padding: 4px;
    box-shadow: var(--shadow-lg);
}

.avatar-image {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid white;
}

.avatar-status {
    position: absolute;
    bottom: 8px;
    right: 8px;
    width: 24px;
    height: 24px;
    background: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-md);
}

.status-dot {
    width: 12px;
    height: 12px;
    background: var(--success-color);
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.1); opacity: 0.8; }
    100% { transform: scale(1); opacity: 1; }
}

/* 文字区域 */
.hero-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--space-sm);
    letter-spacing: -0.02em;
}

.hero-subtitle {
    font-size: 1.1rem;
    color: var(--text-secondary);
    margin-bottom: var(--space-lg);
    font-weight: 500;
}

/* 统计药丸 */
.hero-stats {
    display: flex;
    gap: var(--space-md);
    justify-content: center;
}

.stat-pill {
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-full);
    padding: var(--space-sm) var(--space-md);
    display: flex;
    align-items: center;
    gap: var(--space-xs);
    box-shadow: var(--shadow-sm);
    backdrop-filter: blur(10px);
}

.stat-value {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--highlight-color);
}

.stat-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: 500;
}

/* 快速操作区域 */
.quick-section {
    padding: var(--space-lg);
    background: var(--surface-color);
}

.section-title {
    margin-bottom: var(--space-xl);
    text-align: center;
}

.section-title h2 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--space-xs);
    letter-spacing: -0.01em;
}

.section-title p {
    font-size: 1rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.quick-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-md);
}

.quick-card {
    background: var(--surface-elevated);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    padding: var(--space-lg);
    cursor: pointer;
    transition: all 0.3s var(--ease-out);
    position: relative;
    overflow: hidden;
}

.quick-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity 0.3s var(--ease-out);
}

.quick-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
    border-color: transparent;
}

.quick-card:hover::before {
    opacity: 0.05;
}

.quick-card:active {
    transform: translateY(-2px);
}

.card-icon {
    margin-bottom: var(--space-md);
    position: relative;
    z-index: 1;
}

.icon-bg {
    width: 48px;
    height: 48px;
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
}

.gallery-bg {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.about-bg {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.timeline-bg {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.favorites-bg {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.card-content {
    position: relative;
    z-index: 1;
}

.card-content h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--space-xs);
}

.card-content p {
    font-size: 0.9rem;
    color: var(--text-secondary);
    line-height: 1.4;
}

.card-arrow {
    position: absolute;
    top: var(--space-md);
    right: var(--space-md);
    font-size: 1.2rem;
    color: var(--text-tertiary);
    transition: all 0.3s var(--ease-out);
    z-index: 1;
}

.quick-card:hover .card-arrow {
    color: var(--highlight-color);
    transform: translateX(4px);
}

/* 相册页面 */
.gallery-controls {
    padding: 15px 20px;
    background: var(--surface-color);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 15px;
}

.control-group {
    display: flex;
    gap: 10px;
}

.control-select {
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-small);
    background: var(--background-color);
    color: var(--text-primary);
    font-size: 0.85rem;
    cursor: pointer;
}

.photo-info {
    display: flex;
    gap: 15px;
    font-size: 0.85rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.photo-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2px;
    padding: 0;
}

.photo-item {
    aspect-ratio: 1;
    position: relative;
    cursor: pointer;
    overflow: hidden;
    background: var(--border-color);
}

.photo-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.photo-item:hover img {
    transform: scale(1.05);
}

.photo-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition);
}

.photo-item:hover .photo-overlay {
    opacity: 1;
}

.photo-overlay-content {
    text-align: center;
    color: white;
}

.photo-overlay h3 {
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 4px;
}

.photo-overlay p {
    font-size: 0.75rem;
    opacity: 0.9;
}

/* 分页 */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
    padding: 20px;
    background: var(--surface-color);
    border-top: 1px solid var(--border-color);
}

.page-btn {
    min-width: 36px;
    height: 36px;
    border: none;
    border-radius: var(--border-radius-small);
    background: var(--background-color);
    color: var(--text-primary);
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.page-btn:hover {
    background: var(--border-color);
}

.page-btn.active {
    background: var(--gradient-primary);
    color: white;
}

.page-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* 底部导航栏 - 现代设计 */
.bottom-nav {
    display: flex;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-top: 1px solid var(--border-light);
    padding: var(--space-sm) 0 max(var(--space-sm), env(safe-area-inset-bottom));
    position: relative;
    z-index: 1000;
    box-shadow: 0 -2px 20px rgba(0, 0, 0, 0.08);
}

.nav-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--space-sm) var(--space-xs);
    cursor: pointer;
    transition: all 0.3s var(--ease-out);
    position: relative;
    -webkit-tap-highlight-color: transparent;
    border-radius: var(--radius-md);
    margin: 0 var(--space-xs);
}

.nav-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--highlight-color);
    border-radius: var(--radius-md);
    opacity: 0;
    transform: scale(0.8);
    transition: all 0.3s var(--ease-spring);
}

.nav-item.active::before {
    opacity: 0.1;
    transform: scale(1);
}

.nav-item.active {
    color: var(--highlight-color);
}

.nav-icon {
    font-size: 1.4rem;
    margin-bottom: var(--space-xs);
    transition: all 0.3s var(--ease-out);
    position: relative;
    z-index: 1;
}

.nav-item.active .nav-icon {
    transform: scale(1.1);
}

.nav-label {
    font-size: 0.75rem;
    font-weight: 500;
    color: var(--text-secondary);
    transition: all 0.3s var(--ease-out);
    position: relative;
    z-index: 1;
}

.nav-item.active .nav-label {
    color: var(--highlight-color);
    font-weight: 600;
}

.nav-badge {
    position: absolute;
    top: 2px;
    right: 18%;
    background: var(--error-color);
    color: white;
    font-size: 0.65rem;
    font-weight: 700;
    padding: 2px 6px;
    border-radius: var(--radius-full);
    min-width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    transform: scale(0);
    transition: transform 0.4s var(--ease-spring);
    z-index: 2;
    border: 2px solid white;
    box-shadow: var(--shadow-sm);
}

.nav-badge.show {
    transform: scale(1);
}

/* 页面头部样式 */
.page-header {
    padding: var(--space-lg) var(--space-lg) var(--space-md);
    background: var(--surface-elevated);
    border-bottom: 1px solid var(--border-light);
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: sticky;
    top: 0;
    z-index: 100;
    backdrop-filter: blur(20px);
}

.page-title {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    letter-spacing: -0.01em;
}

.page-subtitle {
    font-size: 1rem;
    color: var(--text-secondary);
    margin-top: var(--space-xs);
    font-weight: 500;
}

.title-icon {
    font-size: 1.5rem;
}

.header-actions {
    display: flex;
    gap: var(--space-sm);
}

.header-btn {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: var(--radius-md);
    background: var(--background-color);
    color: var(--text-primary);
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.3s var(--ease-out);
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid var(--border-light);
}

.header-btn:hover {
    background: var(--surface-color);
    transform: scale(1.05);
    box-shadow: var(--shadow-sm);
}

/* 关于页面样式 */
.about-content {
    padding: var(--space-lg);
}

.profile-card {
    background: var(--surface-elevated);
    border-radius: var(--radius-lg);
    padding: var(--space-xl);
    margin-bottom: var(--space-lg);
    box-shadow: var(--shadow-md);
    text-align: center;
    border: 1px solid var(--border-light);
}

.profile-image {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    overflow: hidden;
    margin: 0 auto var(--space-md);
    border: 4px solid var(--highlight-color);
    box-shadow: var(--shadow-md);
}

.profile-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.profile-info h2 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--space-sm);
    letter-spacing: -0.01em;
}

.profile-desc {
    color: var(--text-secondary);
    font-size: 1rem;
    margin-bottom: var(--space-lg);
    font-weight: 500;
}

.profile-stats {
    display: flex;
    justify-content: space-around;
    gap: var(--space-lg);
}

.profile-stat {
    text-align: center;
}

.profile-stat .stat-label {
    font-size: 0.8rem;
    color: var(--text-tertiary);
    margin-bottom: var(--space-xs);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.profile-stat .stat-value {
    font-size: 0.95rem;
    font-weight: 600;
    color: var(--text-primary);
}

.info-cards {
    display: flex;
    flex-direction: column;
    gap: var(--space-md);
}

.info-card {
    background: var(--surface-elevated);
    border-radius: var(--radius-lg);
    padding: var(--space-lg);
    box-shadow: var(--shadow-sm);
    display: flex;
    align-items: flex-start;
    gap: var(--space-md);
    border: 1px solid var(--border-light);
    transition: all 0.3s var(--ease-out);
}

.info-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.card-icon {
    font-size: 1.8rem;
    flex-shrink: 0;
}

.card-content h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--space-sm);
    letter-spacing: -0.01em;
}

.card-content p {
    font-size: 0.9rem;
    color: var(--text-secondary);
    line-height: 1.6;
    font-weight: 500;
}

/* 时间线页面样式 */
.timeline-content {
    padding: var(--space-lg);
}

.timeline {
    position: relative;
    padding-left: var(--space-xl);
}

.timeline::before {
    content: '';
    position: absolute;
    left: var(--space-md);
    top: 0;
    bottom: 0;
    width: 2px;
    background: linear-gradient(to bottom, var(--highlight-color), var(--border-color));
    border-radius: 1px;
}

.timeline-item {
    position: relative;
    margin-bottom: var(--space-xl);
}

.timeline-date {
    font-size: 0.8rem;
    color: var(--text-tertiary);
    font-weight: 600;
    margin-bottom: var(--space-sm);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.timeline-content-item {
    background: var(--surface-elevated);
    border-radius: var(--radius-lg);
    padding: var(--space-lg);
    box-shadow: var(--shadow-sm);
    display: flex;
    align-items: flex-start;
    gap: var(--space-md);
    position: relative;
    border: 1px solid var(--border-light);
    transition: all 0.3s var(--ease-out);
}

.timeline-content-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.timeline-content-item::before {
    content: '';
    position: absolute;
    left: -26px;
    top: 24px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: var(--highlight-color);
    border: 3px solid var(--surface-elevated);
    box-shadow: var(--shadow-sm);
}

.timeline-icon {
    font-size: 1.5rem;
    flex-shrink: 0;
}

.timeline-text h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--space-xs);
    letter-spacing: -0.01em;
}

.timeline-text p {
    font-size: 0.9rem;
    color: var(--text-secondary);
    line-height: 1.6;
    font-weight: 500;
}

/* 收藏页面样式 */
.favorites-content {
    padding: var(--space-lg);
}

.empty-state {
    text-align: center;
    padding: var(--space-2xl) var(--space-lg);
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: var(--space-lg);
    opacity: 0.4;
}

.empty-state h3 {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--space-sm);
    letter-spacing: -0.01em;
}

.empty-state p {
    color: var(--text-secondary);
    margin-bottom: var(--space-xl);
    font-size: 1rem;
    font-weight: 500;
}

.favorites-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2px;
}

.favorite-item {
    aspect-ratio: 1;
    position: relative;
    cursor: pointer;
    overflow: hidden;
    background: var(--border-light);
    border-radius: var(--radius-sm);
    transition: all 0.3s var(--ease-out);
}

.favorite-item:hover {
    transform: scale(1.02);
    box-shadow: var(--shadow-md);
}

.favorite-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.3s var(--ease-out);
}

.favorite-item:hover img {
    transform: scale(1.05);
}

.favorite-overlay {
    position: absolute;
    top: var(--space-sm);
    right: var(--space-sm);
    width: 28px;
    height: 28px;
    background: rgba(0, 0, 0, 0.7);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--error-color);
    font-size: 0.9rem;
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.2);
}

/* 通用按钮样式 */
.action-btn {
    padding: var(--space-md) var(--space-lg);
    border: none;
    border-radius: var(--radius-md);
    font-size: 0.95rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s var(--ease-out);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-sm);
    text-decoration: none;
    font-family: inherit;
}

.action-btn.primary {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow-sm);
}

.action-btn.primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.action-btn.secondary {
    background: var(--surface-elevated);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.action-btn.secondary:hover {
    background: var(--surface-color);
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.btn-icon {
    font-size: 1.1rem;
}

/* 照片查看器样式 */
.photo-viewer {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #000;
    z-index: 2000;
    display: none;
    flex-direction: column;
    opacity: 0;
    transition: opacity 0.3s var(--ease-out);
}

.photo-viewer.active {
    display: flex;
    opacity: 1;
}

.viewer-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-lg) var(--space-lg);
    background: rgba(0, 0, 0, 0.9);
    color: white;
    backdrop-filter: blur(20px);
}

.viewer-btn {
    width: 44px;
    height: 44px;
    border: none;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.15);
    color: white;
    font-size: 1.3rem;
    cursor: pointer;
    transition: all 0.3s var(--ease-out);
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
}

.viewer-btn:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: scale(1.05);
}

.viewer-title {
    font-size: 1rem;
    font-weight: 600;
    opacity: 0.9;
}

.viewer-content {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    padding: var(--space-lg);
}

.nav-btn {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 56px;
    height: 56px;
    border: none;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.15);
    color: white;
    font-size: 1.8rem;
    cursor: pointer;
    transition: all 0.3s var(--ease-out);
    z-index: 10;
    backdrop-filter: blur(10px);
}

.prev-btn {
    left: var(--space-lg);
}

.next-btn {
    right: var(--space-lg);
}

.nav-btn:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: translateY(-50%) scale(1.05);
}

.photo-container {
    position: relative;
    max-width: 90%;
    max-height: 80%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.viewer-photo {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-xl);
}

.photo-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: none;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.viewer-info {
    padding: var(--space-lg);
    background: rgba(0, 0, 0, 0.9);
    color: white;
    text-align: center;
    backdrop-filter: blur(20px);
}

.photo-title {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: var(--space-sm);
}

.photo-description {
    font-size: 1rem;
    opacity: 0.8;
    line-height: 1.6;
    font-weight: 500;
}

/* 通知组件样式 */
.toast {
    position: fixed;
    top: var(--space-lg);
    left: 50%;
    transform: translateX(-50%) translateY(-100%);
    background: var(--surface-elevated);
    color: var(--text-primary);
    padding: var(--space-md) var(--space-lg);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-lg);
    z-index: 3000;
    transition: transform 0.4s var(--ease-spring);
    font-size: 0.95rem;
    font-weight: 500;
    border-left: 4px solid var(--highlight-color);
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-light);
    max-width: 90%;
}

.toast.show {
    transform: translateX(-50%) translateY(0);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .hero-section {
        padding: var(--space-xl) var(--space-md);
        min-height: 40vh;
    }

    .hero-content {
        padding: var(--space-lg);
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-stats {
        flex-direction: column;
        align-items: center;
        gap: var(--space-sm);
    }

    .quick-grid {
        grid-template-columns: 1fr;
        gap: var(--space-sm);
    }

    .page-title {
        font-size: 1.5rem;
    }

    .photo-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .profile-stats {
        flex-direction: column;
        gap: var(--space-md);
    }

    .info-cards .info-card {
        flex-direction: column;
        text-align: center;
        gap: var(--space-sm);
    }

    .timeline {
        padding-left: var(--space-lg);
    }

    .timeline::before {
        left: var(--space-sm);
    }

    .timeline-content-item::before {
        left: -21px;
    }

    .nav-btn {
        width: 48px;
        height: 48px;
        font-size: 1.5rem;
    }

    .prev-btn {
        left: var(--space-sm);
    }

    .next-btn {
        right: var(--space-sm);
    }
}

@media (max-width: 480px) {
    .hero-section {
        padding: var(--space-lg) var(--space-md);
    }

    .hero-title {
        font-size: 1.75rem;
    }

    .avatar-ring {
        width: 100px;
        height: 100px;
    }

    .quick-section {
        padding: var(--space-md);
    }

    .page-header {
        padding: var(--space-md);
    }

    .page-title {
        font-size: 1.3rem;
    }

    .about-content,
    .timeline-content,
    .favorites-content {
        padding: var(--space-md);
    }

    .profile-card {
        padding: var(--space-lg);
    }

    .favorites-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--space-xs);
    }

    .viewer-content {
        padding: var(--space-sm);
    }

    .nav-btn {
        width: 40px;
        height: 40px;
        font-size: 1.3rem;
    }

    .toast {
        left: var(--space-sm);
        right: var(--space-sm);
        transform: translateY(-100%);
        max-width: none;
    }

    .toast.show {
        transform: translateY(0);
    }
}

/* 关于页面 */
.about-content {
    padding: 20px;
}

.profile-card {
    background: var(--surface-color);
    border-radius: var(--border-radius);
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: var(--shadow-light);
    text-align: center;
}

.profile-image {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    overflow: hidden;
    margin: 0 auto 15px;
    border: 3px solid var(--primary-color);
}

.profile-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.profile-info h2 {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 8px;
}

.profile-desc {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-bottom: 15px;
}

.profile-stats {
    display: flex;
    justify-content: space-around;
    gap: 15px;
}

.profile-stat {
    text-align: center;
}

.profile-stat .stat-label {
    font-size: 0.75rem;
    color: var(--text-light);
    margin-bottom: 4px;
}

.profile-stat .stat-value {
    font-size: 0.85rem;
    font-weight: 600;
    color: var(--text-primary);
}

.info-cards {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.info-card {
    background: var(--surface-color);
    border-radius: var(--border-radius);
    padding: 16px;
    box-shadow: var(--shadow-light);
    display: flex;
    align-items: flex-start;
    gap: 12px;
}

.card-icon {
    font-size: 1.5rem;
    flex-shrink: 0;
}

.card-content h3 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 6px;
}

.card-content p {
    font-size: 0.85rem;
    color: var(--text-secondary);
    line-height: 1.5;
}

/* 时间线页面 */
.timeline-content {
    padding: 20px;
}

.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: var(--border-color);
}

.timeline-item {
    position: relative;
    margin-bottom: 30px;
}

.timeline-date {
    font-size: 0.75rem;
    color: var(--text-light);
    font-weight: 500;
    margin-bottom: 8px;
}

.timeline-content-item {
    background: var(--surface-color);
    border-radius: var(--border-radius);
    padding: 16px;
    box-shadow: var(--shadow-light);
    display: flex;
    align-items: flex-start;
    gap: 12px;
    position: relative;
}

.timeline-content-item::before {
    content: '';
    position: absolute;
    left: -22px;
    top: 20px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--primary-color);
    border: 2px solid var(--surface-color);
}

.timeline-icon {
    font-size: 1.3rem;
    flex-shrink: 0;
}

.timeline-text h3 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.timeline-text p {
    font-size: 0.85rem;
    color: var(--text-secondary);
    line-height: 1.5;
}

/* 收藏页面 */
.favorites-content {
    padding: 20px;
}

.empty-state {
    text-align: center;
    padding: 40px 20px;
}

.empty-icon {
    font-size: 3rem;
    margin-bottom: 15px;
    opacity: 0.5;
}

.empty-state h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 8px;
}

.empty-state p {
    color: var(--text-secondary);
    margin-bottom: 20px;
}

.favorites-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2px;
}

.favorite-item {
    aspect-ratio: 1;
    position: relative;
    cursor: pointer;
    overflow: hidden;
    background: var(--border-color);
}

.favorite-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.favorite-overlay {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 24px;
    height: 24px;
    background: rgba(0, 0, 0, 0.6);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    font-size: 0.8rem;
}

/* 照片查看器 */
.photo-viewer {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #000;
    z-index: 2000;
    display: none;
    flex-direction: column;
}

.photo-viewer.active {
    display: flex;
}

.viewer-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
}

.viewer-btn {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.viewer-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

.viewer-title {
    font-size: 1rem;
    font-weight: 500;
}

.viewer-content {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.nav-btn {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 50px;
    height: 50px;
    border: none;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    transition: var(--transition);
    z-index: 10;
}

.prev-btn {
    left: 20px;
}

.next-btn {
    right: 20px;
}

.nav-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

.photo-container {
    position: relative;
    max-width: 90%;
    max-height: 80%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.viewer-photo {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    border-radius: 8px;
}

.photo-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: none;
}

.loading-spinner {
    width: 30px;
    height: 30px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.viewer-info {
    padding: 20px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    text-align: center;
}

.photo-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 8px;
}

.photo-description {
    font-size: 0.9rem;
    opacity: 0.8;
    line-height: 1.5;
}

/* 通知组件 */
.toast {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%) translateY(-100%);
    background: var(--surface-color);
    color: var(--text-primary);
    padding: 12px 20px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-medium);
    z-index: 3000;
    transition: transform 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    font-size: 0.9rem;
    font-weight: 500;
    border-left: 4px solid var(--primary-color);
}

.toast.show {
    transform: translateX(-50%) translateY(0);
}

/* 响应式设计 */
@media (max-width: 480px) {
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
    }

    .quick-actions {
        flex-direction: column;
    }

    .photo-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .profile-stats {
        flex-direction: column;
        gap: 10px;
    }

    .info-cards .info-card {
        flex-direction: column;
        text-align: center;
    }

    .timeline {
        padding-left: 20px;
    }

    .timeline::before {
        left: 10px;
    }

    .timeline-content-item::before {
        left: -17px;
    }
}
