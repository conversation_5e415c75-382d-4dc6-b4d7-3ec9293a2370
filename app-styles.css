/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* CSS变量 */
:root {
    --primary-color: #ff6b9d;
    --secondary-color: #764ba2;
    --accent-color: #f093fb;
    --background-color: #f8f9ff;
    --surface-color: #ffffff;
    --text-primary: #2d3748;
    --text-secondary: #718096;
    --text-light: #a0aec0;
    --border-color: #e2e8f0;
    --shadow-light: 0 2px 10px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 4px 20px rgba(0, 0, 0, 0.15);
    --shadow-heavy: 0 8px 30px rgba(0, 0, 0, 0.2);
    --gradient-primary: linear-gradient(135deg, #ff6b9d 0%, #c44569 100%);
    --gradient-secondary: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
    --gradient-accent: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --border-radius: 16px;
    --border-radius-small: 8px;
    --transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* 基础样式 */
body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    background: var(--background-color);
    color: var(--text-primary);
    line-height: 1.6;
    overflow: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* 应用容器 */
.app-container {
    width: 100vw;
    height: 100vh;
    display: flex;
    flex-direction: column;
    background: var(--background-color);
    position: relative;
    overflow: hidden;
}

/* 状态栏 */
.status-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 20px;
    background: var(--surface-color);
    border-bottom: 1px solid var(--border-color);
    font-size: 0.85rem;
    font-weight: 500;
    color: var(--text-primary);
    z-index: 1000;
}

.status-left, .status-right {
    display: flex;
    align-items: center;
    gap: 8px;
}

.time {
    font-weight: 600;
}

/* 页面容器 */
.page-container {
    flex: 1;
    position: relative;
    overflow: hidden;
}

/* 页面基础样式 */
.page {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--background-color);
    transform: translateX(100%);
    transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    overflow-y: auto;
    padding-bottom: 80px;
}

.page.active {
    transform: translateX(0);
}

.page.prev {
    transform: translateX(-100%);
}

/* 页面头部 */
.page-header {
    padding: 20px 20px 10px;
    background: var(--surface-color);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: sticky;
    top: 0;
    z-index: 100;
    backdrop-filter: blur(10px);
}

.page-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 8px;
}

.page-subtitle {
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin-top: 4px;
}

.title-icon {
    font-size: 1.3rem;
}

.header-actions {
    display: flex;
    gap: 8px;
}

.header-btn {
    width: 36px;
    height: 36px;
    border: none;
    border-radius: var(--border-radius-small);
    background: var(--background-color);
    color: var(--text-primary);
    font-size: 1.1rem;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.header-btn:hover {
    background: var(--border-color);
    transform: scale(1.05);
}

/* 首页样式 */
.home-page {
    padding: 0;
}

.hero-card {
    margin: 20px;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-medium);
    position: relative;
}

.hero-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.hero-photo {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.hero-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
    color: white;
    padding: 20px;
}

.hero-text h2 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 4px;
}

.hero-text p {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* 统计区域 */
.stats-section {
    padding: 20px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 15px;
}

.stat-item {
    background: var(--surface-color);
    padding: 16px 12px;
    border-radius: var(--border-radius);
    text-align: center;
    box-shadow: var(--shadow-light);
    transition: var(--transition);
}

.stat-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.stat-icon {
    font-size: 1.5rem;
    margin-bottom: 8px;
}

.stat-number {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 4px;
}

.stat-label {
    font-size: 0.8rem;
    color: var(--text-secondary);
    font-weight: 500;
}

/* 快速操作 */
.quick-actions {
    padding: 0 20px 20px;
    display: flex;
    gap: 12px;
}

.action-btn {
    flex: 1;
    padding: 16px;
    border: none;
    border-radius: var(--border-radius);
    font-size: 0.95rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.action-btn.primary {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow-light);
}

.action-btn.secondary {
    background: var(--surface-color);
    color: var(--text-primary);
    border: 2px solid var(--border-color);
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.btn-icon {
    font-size: 1.1rem;
}

/* 相册页面 */
.gallery-controls {
    padding: 15px 20px;
    background: var(--surface-color);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 15px;
}

.control-group {
    display: flex;
    gap: 10px;
}

.control-select {
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-small);
    background: var(--background-color);
    color: var(--text-primary);
    font-size: 0.85rem;
    cursor: pointer;
}

.photo-info {
    display: flex;
    gap: 15px;
    font-size: 0.85rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.photo-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2px;
    padding: 0;
}

.photo-item {
    aspect-ratio: 1;
    position: relative;
    cursor: pointer;
    overflow: hidden;
    background: var(--border-color);
}

.photo-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.photo-item:hover img {
    transform: scale(1.05);
}

.photo-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition);
}

.photo-item:hover .photo-overlay {
    opacity: 1;
}

.photo-overlay-content {
    text-align: center;
    color: white;
}

.photo-overlay h3 {
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 4px;
}

.photo-overlay p {
    font-size: 0.75rem;
    opacity: 0.9;
}

/* 分页 */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
    padding: 20px;
    background: var(--surface-color);
    border-top: 1px solid var(--border-color);
}

.page-btn {
    min-width: 36px;
    height: 36px;
    border: none;
    border-radius: var(--border-radius-small);
    background: var(--background-color);
    color: var(--text-primary);
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.page-btn:hover {
    background: var(--border-color);
}

.page-btn.active {
    background: var(--gradient-primary);
    color: white;
}

.page-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* 底部导航栏 */
.bottom-nav {
    display: flex;
    background: var(--surface-color);
    border-top: 1px solid var(--border-color);
    padding: 8px 0 max(8px, env(safe-area-inset-bottom));
    position: relative;
    z-index: 1000;
}

.nav-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 8px 4px;
    cursor: pointer;
    transition: var(--transition);
    position: relative;
    -webkit-tap-highlight-color: transparent;
}

.nav-item.active {
    color: var(--primary-color);
}

.nav-icon {
    font-size: 1.3rem;
    margin-bottom: 4px;
    transition: var(--transition);
}

.nav-item.active .nav-icon {
    transform: scale(1.1);
}

.nav-label {
    font-size: 0.7rem;
    font-weight: 500;
    opacity: 0.8;
}

.nav-item.active .nav-label {
    opacity: 1;
    font-weight: 600;
}

.nav-badge {
    position: absolute;
    top: 4px;
    right: 20%;
    background: var(--primary-color);
    color: white;
    font-size: 0.6rem;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    transform: scale(0);
    transition: transform 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.nav-badge.show {
    transform: scale(1);
}

/* 关于页面 */
.about-content {
    padding: 20px;
}

.profile-card {
    background: var(--surface-color);
    border-radius: var(--border-radius);
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: var(--shadow-light);
    text-align: center;
}

.profile-image {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    overflow: hidden;
    margin: 0 auto 15px;
    border: 3px solid var(--primary-color);
}

.profile-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.profile-info h2 {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 8px;
}

.profile-desc {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-bottom: 15px;
}

.profile-stats {
    display: flex;
    justify-content: space-around;
    gap: 15px;
}

.profile-stat {
    text-align: center;
}

.profile-stat .stat-label {
    font-size: 0.75rem;
    color: var(--text-light);
    margin-bottom: 4px;
}

.profile-stat .stat-value {
    font-size: 0.85rem;
    font-weight: 600;
    color: var(--text-primary);
}

.info-cards {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.info-card {
    background: var(--surface-color);
    border-radius: var(--border-radius);
    padding: 16px;
    box-shadow: var(--shadow-light);
    display: flex;
    align-items: flex-start;
    gap: 12px;
}

.card-icon {
    font-size: 1.5rem;
    flex-shrink: 0;
}

.card-content h3 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 6px;
}

.card-content p {
    font-size: 0.85rem;
    color: var(--text-secondary);
    line-height: 1.5;
}

/* 时间线页面 */
.timeline-content {
    padding: 20px;
}

.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: var(--border-color);
}

.timeline-item {
    position: relative;
    margin-bottom: 30px;
}

.timeline-date {
    font-size: 0.75rem;
    color: var(--text-light);
    font-weight: 500;
    margin-bottom: 8px;
}

.timeline-content-item {
    background: var(--surface-color);
    border-radius: var(--border-radius);
    padding: 16px;
    box-shadow: var(--shadow-light);
    display: flex;
    align-items: flex-start;
    gap: 12px;
    position: relative;
}

.timeline-content-item::before {
    content: '';
    position: absolute;
    left: -22px;
    top: 20px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--primary-color);
    border: 2px solid var(--surface-color);
}

.timeline-icon {
    font-size: 1.3rem;
    flex-shrink: 0;
}

.timeline-text h3 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.timeline-text p {
    font-size: 0.85rem;
    color: var(--text-secondary);
    line-height: 1.5;
}

/* 收藏页面 */
.favorites-content {
    padding: 20px;
}

.empty-state {
    text-align: center;
    padding: 40px 20px;
}

.empty-icon {
    font-size: 3rem;
    margin-bottom: 15px;
    opacity: 0.5;
}

.empty-state h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 8px;
}

.empty-state p {
    color: var(--text-secondary);
    margin-bottom: 20px;
}

.favorites-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2px;
}

.favorite-item {
    aspect-ratio: 1;
    position: relative;
    cursor: pointer;
    overflow: hidden;
    background: var(--border-color);
}

.favorite-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.favorite-overlay {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 24px;
    height: 24px;
    background: rgba(0, 0, 0, 0.6);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    font-size: 0.8rem;
}

/* 照片查看器 */
.photo-viewer {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #000;
    z-index: 2000;
    display: none;
    flex-direction: column;
}

.photo-viewer.active {
    display: flex;
}

.viewer-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
}

.viewer-btn {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.viewer-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

.viewer-title {
    font-size: 1rem;
    font-weight: 500;
}

.viewer-content {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.nav-btn {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 50px;
    height: 50px;
    border: none;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    transition: var(--transition);
    z-index: 10;
}

.prev-btn {
    left: 20px;
}

.next-btn {
    right: 20px;
}

.nav-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

.photo-container {
    position: relative;
    max-width: 90%;
    max-height: 80%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.viewer-photo {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    border-radius: 8px;
}

.photo-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: none;
}

.loading-spinner {
    width: 30px;
    height: 30px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.viewer-info {
    padding: 20px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    text-align: center;
}

.photo-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 8px;
}

.photo-description {
    font-size: 0.9rem;
    opacity: 0.8;
    line-height: 1.5;
}

/* 通知组件 */
.toast {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%) translateY(-100%);
    background: var(--surface-color);
    color: var(--text-primary);
    padding: 12px 20px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-medium);
    z-index: 3000;
    transition: transform 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    font-size: 0.9rem;
    font-weight: 500;
    border-left: 4px solid var(--primary-color);
}

.toast.show {
    transform: translateX(-50%) translateY(0);
}

/* 响应式设计 */
@media (max-width: 480px) {
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
    }

    .quick-actions {
        flex-direction: column;
    }

    .photo-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .profile-stats {
        flex-direction: column;
        gap: 10px;
    }

    .info-cards .info-card {
        flex-direction: column;
        text-align: center;
    }

    .timeline {
        padding-left: 20px;
    }

    .timeline::before {
        left: 10px;
    }

    .timeline-content-item::before {
        left: -17px;
    }
}
