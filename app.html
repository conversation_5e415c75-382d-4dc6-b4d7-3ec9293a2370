<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小宝的成长记录 - APP版</title>
    <link rel="stylesheet" href="app-styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- 应用容器 -->
    <div class="app-container">
        <!-- 页面容器 -->
        <div class="page-container">
            <!-- 首页 -->
            <div class="page home-page active" id="homePage">
                <!-- 主视觉区域 -->
                <div class="hero-section">
                    <div class="hero-background">
                        <div class="gradient-orb orb-1"></div>
                        <div class="gradient-orb orb-2"></div>
                        <div class="gradient-orb orb-3"></div>
                    </div>

                    <!-- 黄金分割布局：上部分占61.8% -->
                    <div class="hero-primary">
                        <div class="hero-avatar">
                            <div class="avatar-ring">
                                <img src="image/微信图片_20250704194141.jpg" alt="小宝" class="avatar-image">
                            </div>
                            <div class="avatar-status">
                                <div class="status-dot"></div>
                            </div>
                        </div>

                        <div class="hero-text">
                            <h1 class="hero-title">小宝</h1>
                            <p class="hero-subtitle">我的小天使 ✨</p>
                        </div>
                    </div>

                    <!-- 黄金分割布局：下部分占38.2% -->
                    <div class="hero-secondary">
                        <div class="hero-stats">
                            <div class="stat-pill">
                                <span class="stat-value" id="totalPhotos">30</span>
                                <span class="stat-label">张照片</span>
                            </div>
                            <div class="stat-pill">
                                <span class="stat-value" id="favoriteCount">0</span>
                                <span class="stat-label">个收藏</span>
                            </div>
                            <div class="stat-pill">
                                <span class="stat-value">∞</span>
                                <span class="stat-label">份爱</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 快速操作区域 -->
                <div class="quick-section">
                    <div class="section-title">
                        <h2>快速访问</h2>
                        <p>探索小宝的美好世界</p>
                    </div>

                    <div class="quick-grid">
                        <div class="quick-card gallery-card" onclick="switchPage('gallery')">
                            <div class="card-icon">
                                <div class="icon-bg gallery-bg">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                                        <path d="M4 4h16v16H4V4zm2 2v12h12V6H6zm2 2h8v6H8V8zm2 2v2h4v-2h-4z" fill="currentColor"/>
                                    </svg>
                                </div>
                            </div>
                            <div class="card-content">
                                <h3>相册</h3>
                                <p>浏览所有珍贵照片</p>
                            </div>
                            <div class="card-arrow">→</div>
                        </div>

                        <div class="quick-card about-card" onclick="switchPage('about')">
                            <div class="card-icon">
                                <div class="icon-bg about-bg">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                                        <circle cx="12" cy="8" r="3" fill="currentColor"/>
                                        <path d="M12 14c-4 0-7 2-7 4v2h14v-2c0-2-3-4-7-4z" fill="currentColor"/>
                                    </svg>
                                </div>
                            </div>
                            <div class="card-content">
                                <h3>关于</h3>
                                <p>了解小宝的故事</p>
                            </div>
                            <div class="card-arrow">→</div>
                        </div>

                        <div class="quick-card timeline-card" onclick="switchPage('timeline')">
                            <div class="card-icon">
                                <div class="icon-bg timeline-bg">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm.5-13H11v6l5.25 3.15.75-1.23-4.5-2.67V7z" fill="currentColor"/>
                                    </svg>
                                </div>
                            </div>
                            <div class="card-content">
                                <h3>成长</h3>
                                <p>记录成长时光</p>
                            </div>
                            <div class="card-arrow">→</div>
                        </div>

                        <div class="quick-card favorites-card" onclick="switchPage('favorites')">
                            <div class="card-icon">
                                <div class="icon-bg favorites-bg">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                                        <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z" fill="currentColor"/>
                                    </svg>
                                </div>
                            </div>
                            <div class="card-content">
                                <h3>收藏</h3>
                                <p>我的最爱照片</p>
                            </div>
                            <div class="card-arrow">→</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 相册页面 -->
            <div class="page gallery-page" id="galleryPage">
                <div class="page-header">
                    <h1 class="page-title">
                        <span class="title-icon">📸</span>
                        相册
                    </h1>
                    <div class="header-actions">
                        <button class="header-btn" id="viewModeBtn">⊞</button>
                        <button class="header-btn" id="sortBtn">⇅</button>
                    </div>
                </div>

                <div class="gallery-controls">
                    <div class="control-group">
                        <select class="control-select" id="sortSelect">
                            <option value="default">默认排序</option>
                            <option value="date">按日期</option>
                            <option value="name">按名称</option>
                            <option value="random">随机</option>
                        </select>
                        <select class="control-select" id="pageSizeSelect">
                            <option value="9">9张/页</option>
                            <option value="12">12张/页</option>
                            <option value="18">18张/页</option>
                        </select>
                    </div>
                    <div class="photo-info">
                        <span>第 <span id="currentPageNum">1</span> 页</span>
                        <span>共 <span id="totalPhotosCount">30</span> 张</span>
                    </div>
                </div>

                <div class="photo-grid" id="photoGrid">
                    <!-- 照片将通过JavaScript动态生成 -->
                </div>

                <div class="pagination" id="pagination">
                    <!-- 分页将通过JavaScript动态生成 -->
                </div>
            </div>

            <!-- 关于页面 -->
            <div class="page about-page" id="aboutPage">
                <div class="page-header">
                    <h1 class="page-title">
                        <span class="title-icon">👶</span>
                        关于小宝
                    </h1>
                </div>

                <div class="about-content">
                    <div class="profile-card">
                        <div class="profile-image">
                            <img src="image/微信图片_20250704194143.jpg" alt="小宝头像">
                        </div>
                        <div class="profile-info">
                            <h2>小宝</h2>
                            <p class="profile-desc">一个可爱的小天使 👼</p>
                            <div class="profile-stats">
                                <div class="profile-stat">
                                    <span class="stat-label">生日</span>
                                    <span class="stat-value">2024年7月</span>
                                </div>
                                <div class="profile-stat">
                                    <span class="stat-label">星座</span>
                                    <span class="stat-value">巨蟹座 ♋</span>
                                </div>
                                <div class="profile-stat">
                                    <span class="stat-label">性格</span>
                                    <span class="stat-value">活泼可爱</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="info-cards">
                        <div class="info-card">
                            <div class="card-icon">🍼</div>
                            <div class="card-content">
                                <h3>成长记录</h3>
                                <p>记录小宝每一个重要的成长瞬间，从第一次微笑到第一次翻身。</p>
                            </div>
                        </div>
                        <div class="info-card">
                            <div class="card-icon">📸</div>
                            <div class="card-content">
                                <h3>珍贵回忆</h3>
                                <p>用镜头捕捉小宝最自然、最真实的表情和动作。</p>
                            </div>
                        </div>
                        <div class="info-card">
                            <div class="card-icon">💕</div>
                            <div class="card-content">
                                <h3>爱的见证</h3>
                                <p>每一张照片都承载着满满的爱意和美好的祝愿。</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 成长页面 -->
            <div class="page timeline-page" id="timelinePage">
                <div class="page-header">
                    <h1 class="page-title">
                        <span class="title-icon">📅</span>
                        成长时光
                    </h1>
                </div>

                <div class="timeline-content">
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-date">2024年7月</div>
                            <div class="timeline-content-item">
                                <div class="timeline-icon">🎂</div>
                                <div class="timeline-text">
                                    <h3>小宝诞生</h3>
                                    <p>我们的小天使来到了这个世界</p>
                                </div>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-date">2024年8月</div>
                            <div class="timeline-content-item">
                                <div class="timeline-icon">😊</div>
                                <div class="timeline-text">
                                    <h3>第一次微笑</h3>
                                    <p>小宝第一次对我们露出了甜美的笑容</p>
                                </div>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-date">2024年9月</div>
                            <div class="timeline-content-item">
                                <div class="timeline-icon">🤸</div>
                                <div class="timeline-text">
                                    <h3>学会翻身</h3>
                                    <p>小宝开始展现运动天赋，学会了翻身</p>
                                </div>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-date">2024年10月</div>
                            <div class="timeline-content-item">
                                <div class="timeline-icon">🗣️</div>
                                <div class="timeline-text">
                                    <h3>咿呀学语</h3>
                                    <p>小宝开始发出各种可爱的声音</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 收藏页面 -->
            <div class="page favorites-page" id="favoritesPage">
                <div class="page-header">
                    <h1 class="page-title">
                        <span class="title-icon">❤️</span>
                        我的收藏
                    </h1>
                    <div class="header-actions">
                        <button class="header-btn" id="clearFavoritesBtn">🗑️</button>
                    </div>
                </div>

                <div class="favorites-content" id="favoritesContent">
                    <div class="empty-state" id="emptyFavorites">
                        <div class="empty-icon">💔</div>
                        <h3>还没有收藏</h3>
                        <p>去相册里收藏一些喜欢的照片吧！</p>
                        <button class="action-btn primary" onclick="switchPage('gallery')">
                            <span class="btn-icon">📸</span>
                            <span>去相册</span>
                        </button>
                    </div>
                    <div class="favorites-grid" id="favoritesGrid">
                        <!-- 收藏的照片将通过JavaScript动态生成 -->
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部导航栏 -->
        <div class="bottom-nav">
            <div class="nav-item active" data-page="home">
                <div class="nav-icon">🏠</div>
                <div class="nav-label">首页</div>
            </div>
            <div class="nav-item" data-page="gallery">
                <div class="nav-icon">📸</div>
                <div class="nav-label">相册</div>
            </div>
            <div class="nav-item" data-page="about">
                <div class="nav-icon">👶</div>
                <div class="nav-label">关于</div>
            </div>
            <div class="nav-item" data-page="timeline">
                <div class="nav-icon">📅</div>
                <div class="nav-label">成长</div>
            </div>
            <div class="nav-item" data-page="favorites">
                <div class="nav-icon">❤️</div>
                <div class="nav-label">收藏</div>
                <div class="nav-badge" id="navFavoriteBadge">0</div>
            </div>
        </div>
    </div>

    <!-- 照片查看器 -->
    <div class="photo-viewer" id="photoViewer">
        <div class="viewer-header">
            <button class="viewer-btn back-btn" onclick="closePhotoViewer()">←</button>
            <div class="viewer-title">
                <span id="viewerPhotoIndex">1</span> / <span id="viewerPhotoTotal">30</span>
            </div>
            <button class="viewer-btn favorite-btn" id="viewerFavoriteBtn" onclick="togglePhotoFavorite()">❤️</button>
        </div>
        
        <div class="viewer-content">
            <button class="nav-btn prev-btn" onclick="navigatePhoto(-1)">‹</button>
            <div class="photo-container">
                <img class="viewer-photo" id="viewerPhoto" src="" alt="">
                <div class="photo-loading" id="photoLoading">
                    <div class="loading-spinner"></div>
                </div>
            </div>
            <button class="nav-btn next-btn" onclick="navigatePhoto(1)">›</button>
        </div>
        
        <div class="viewer-info">
            <h3 class="photo-title" id="photoTitle">照片标题</h3>
            <p class="photo-description" id="photoDescription">照片描述</p>
        </div>
    </div>



    <script src="https://cdnjs.cloudflare.com/ajax/libs/animejs/3.2.1/anime.min.js"></script>
    <script src="app-script.js"></script>
</body>
</html>
