<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小宝的成长记录 - APP版</title>
    <link rel="stylesheet" href="app-styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- 应用容器 -->
    <div class="app-container">
        <!-- 状态栏 -->
        <div class="status-bar">
            <div class="status-left">
                <span class="time" id="currentTime">12:34</span>
            </div>
            <div class="status-right">
                <span class="battery">🔋 85%</span>
                <span class="signal">📶</span>
            </div>
        </div>

        <!-- 页面容器 -->
        <div class="page-container">
            <!-- 首页 -->
            <div class="page home-page active" id="homePage">
                <div class="page-header">
                    <h1 class="page-title">小宝的世界</h1>
                    <p class="page-subtitle">记录每一个珍贵瞬间 💕</p>
                </div>
                
                <div class="hero-card">
                    <div class="hero-image">
                        <img src="image/微信图片_20250704194141.jpg" alt="小宝" class="hero-photo">
                        <div class="hero-overlay">
                            <div class="hero-text">
                                <h2>今天的小宝</h2>
                                <p>健康快乐每一天</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="stats-section">
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-icon">📸</div>
                            <div class="stat-number" id="totalPhotos">30</div>
                            <div class="stat-label">照片</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-icon">❤️</div>
                            <div class="stat-number" id="favoriteCount">0</div>
                            <div class="stat-label">收藏</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-icon">📅</div>
                            <div class="stat-number">365</div>
                            <div class="stat-label">天数</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-icon">🎂</div>
                            <div class="stat-number">1</div>
                            <div class="stat-label">岁</div>
                        </div>
                    </div>
                </div>

                <div class="quick-actions">
                    <button class="action-btn primary" onclick="switchPage('gallery')">
                        <span class="btn-icon">📸</span>
                        <span>查看相册</span>
                    </button>
                    <button class="action-btn secondary" onclick="switchPage('about')">
                        <span class="btn-icon">👶</span>
                        <span>关于小宝</span>
                    </button>
                </div>
            </div>

            <!-- 相册页面 -->
            <div class="page gallery-page" id="galleryPage">
                <div class="page-header">
                    <h1 class="page-title">
                        <span class="title-icon">📸</span>
                        相册
                    </h1>
                    <div class="header-actions">
                        <button class="header-btn" id="viewModeBtn">⊞</button>
                        <button class="header-btn" id="sortBtn">⇅</button>
                    </div>
                </div>

                <div class="gallery-controls">
                    <div class="control-group">
                        <select class="control-select" id="sortSelect">
                            <option value="default">默认排序</option>
                            <option value="date">按日期</option>
                            <option value="name">按名称</option>
                            <option value="random">随机</option>
                        </select>
                        <select class="control-select" id="pageSizeSelect">
                            <option value="9">9张/页</option>
                            <option value="12">12张/页</option>
                            <option value="18">18张/页</option>
                        </select>
                    </div>
                    <div class="photo-info">
                        <span>第 <span id="currentPageNum">1</span> 页</span>
                        <span>共 <span id="totalPhotosCount">30</span> 张</span>
                    </div>
                </div>

                <div class="photo-grid" id="photoGrid">
                    <!-- 照片将通过JavaScript动态生成 -->
                </div>

                <div class="pagination" id="pagination">
                    <!-- 分页将通过JavaScript动态生成 -->
                </div>
            </div>

            <!-- 关于页面 -->
            <div class="page about-page" id="aboutPage">
                <div class="page-header">
                    <h1 class="page-title">
                        <span class="title-icon">👶</span>
                        关于小宝
                    </h1>
                </div>

                <div class="about-content">
                    <div class="profile-card">
                        <div class="profile-image">
                            <img src="image/微信图片_20250704194143.jpg" alt="小宝头像">
                        </div>
                        <div class="profile-info">
                            <h2>小宝</h2>
                            <p class="profile-desc">一个可爱的小天使 👼</p>
                            <div class="profile-stats">
                                <div class="profile-stat">
                                    <span class="stat-label">生日</span>
                                    <span class="stat-value">2024年7月</span>
                                </div>
                                <div class="profile-stat">
                                    <span class="stat-label">星座</span>
                                    <span class="stat-value">巨蟹座 ♋</span>
                                </div>
                                <div class="profile-stat">
                                    <span class="stat-label">性格</span>
                                    <span class="stat-value">活泼可爱</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="info-cards">
                        <div class="info-card">
                            <div class="card-icon">🍼</div>
                            <div class="card-content">
                                <h3>成长记录</h3>
                                <p>记录小宝每一个重要的成长瞬间，从第一次微笑到第一次翻身。</p>
                            </div>
                        </div>
                        <div class="info-card">
                            <div class="card-icon">📸</div>
                            <div class="card-content">
                                <h3>珍贵回忆</h3>
                                <p>用镜头捕捉小宝最自然、最真实的表情和动作。</p>
                            </div>
                        </div>
                        <div class="info-card">
                            <div class="card-icon">💕</div>
                            <div class="card-content">
                                <h3>爱的见证</h3>
                                <p>每一张照片都承载着满满的爱意和美好的祝愿。</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 成长页面 -->
            <div class="page timeline-page" id="timelinePage">
                <div class="page-header">
                    <h1 class="page-title">
                        <span class="title-icon">📅</span>
                        成长时光
                    </h1>
                </div>

                <div class="timeline-content">
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-date">2024年7月</div>
                            <div class="timeline-content-item">
                                <div class="timeline-icon">🎂</div>
                                <div class="timeline-text">
                                    <h3>小宝诞生</h3>
                                    <p>我们的小天使来到了这个世界</p>
                                </div>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-date">2024年8月</div>
                            <div class="timeline-content-item">
                                <div class="timeline-icon">😊</div>
                                <div class="timeline-text">
                                    <h3>第一次微笑</h3>
                                    <p>小宝第一次对我们露出了甜美的笑容</p>
                                </div>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-date">2024年9月</div>
                            <div class="timeline-content-item">
                                <div class="timeline-icon">🤸</div>
                                <div class="timeline-text">
                                    <h3>学会翻身</h3>
                                    <p>小宝开始展现运动天赋，学会了翻身</p>
                                </div>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-date">2024年10月</div>
                            <div class="timeline-content-item">
                                <div class="timeline-icon">🗣️</div>
                                <div class="timeline-text">
                                    <h3>咿呀学语</h3>
                                    <p>小宝开始发出各种可爱的声音</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 收藏页面 -->
            <div class="page favorites-page" id="favoritesPage">
                <div class="page-header">
                    <h1 class="page-title">
                        <span class="title-icon">❤️</span>
                        我的收藏
                    </h1>
                    <div class="header-actions">
                        <button class="header-btn" id="clearFavoritesBtn">🗑️</button>
                    </div>
                </div>

                <div class="favorites-content" id="favoritesContent">
                    <div class="empty-state" id="emptyFavorites">
                        <div class="empty-icon">💔</div>
                        <h3>还没有收藏</h3>
                        <p>去相册里收藏一些喜欢的照片吧！</p>
                        <button class="action-btn primary" onclick="switchPage('gallery')">
                            <span class="btn-icon">📸</span>
                            <span>去相册</span>
                        </button>
                    </div>
                    <div class="favorites-grid" id="favoritesGrid">
                        <!-- 收藏的照片将通过JavaScript动态生成 -->
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部导航栏 -->
        <div class="bottom-nav">
            <div class="nav-item active" data-page="home">
                <div class="nav-icon">🏠</div>
                <div class="nav-label">首页</div>
            </div>
            <div class="nav-item" data-page="gallery">
                <div class="nav-icon">📸</div>
                <div class="nav-label">相册</div>
            </div>
            <div class="nav-item" data-page="about">
                <div class="nav-icon">👶</div>
                <div class="nav-label">关于</div>
            </div>
            <div class="nav-item" data-page="timeline">
                <div class="nav-icon">📅</div>
                <div class="nav-label">成长</div>
            </div>
            <div class="nav-item" data-page="favorites">
                <div class="nav-icon">❤️</div>
                <div class="nav-label">收藏</div>
                <div class="nav-badge" id="navFavoriteBadge">0</div>
            </div>
        </div>
    </div>

    <!-- 照片查看器 -->
    <div class="photo-viewer" id="photoViewer">
        <div class="viewer-header">
            <button class="viewer-btn back-btn" onclick="closePhotoViewer()">←</button>
            <div class="viewer-title">
                <span id="viewerPhotoIndex">1</span> / <span id="viewerPhotoTotal">30</span>
            </div>
            <button class="viewer-btn favorite-btn" id="viewerFavoriteBtn" onclick="togglePhotoFavorite()">❤️</button>
        </div>
        
        <div class="viewer-content">
            <button class="nav-btn prev-btn" onclick="navigatePhoto(-1)">‹</button>
            <div class="photo-container">
                <img class="viewer-photo" id="viewerPhoto" src="" alt="">
                <div class="photo-loading" id="photoLoading">
                    <div class="loading-spinner"></div>
                </div>
            </div>
            <button class="nav-btn next-btn" onclick="navigatePhoto(1)">›</button>
        </div>
        
        <div class="viewer-info">
            <h3 class="photo-title" id="photoTitle">照片标题</h3>
            <p class="photo-description" id="photoDescription">照片描述</p>
        </div>
    </div>

    <!-- 通知组件 -->
    <div class="toast" id="toast">
        <div class="toast-content" id="toastContent"></div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/animejs/3.2.1/anime.min.js"></script>
    <script src="app-script.js"></script>
</body>
</html>
